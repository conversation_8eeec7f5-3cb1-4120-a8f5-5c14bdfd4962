# ESLint Unused Imports Guide

This project has been configured with ESLint to detect and automatically fix unused imports, which helps maintain clean code and improve performance.

## How It Works

The ESLint configuration in this project includes specific rules to:

1. Detect unused imports in your TypeScript/JavaScript files
2. Automatically remove them with a simple command
3. Warn about other unused variables to help keep your code clean

## Available Commands

- `pnpm run lint` - Run the standard Next.js linting
- `pnpm run lint:unused` - Check for unused imports in your code (dry run, no changes made)
- `pnpm run lint:fix` - Automatically remove unused imports across the project

## Examples

### Finding Unused Imports

To check for unused imports without making changes:

```bash
pnpm run lint:unused
```

This will scan your project and report all unused imports.

### Fixing a Specific File

To fix unused imports in a specific file:

```bash
pnpm run lint:fix -- src/path/to/your/file.tsx
```

### Fixing All Files

To fix all unused imports across the project:

```bash
pnpm run lint:fix
```

## Best Practices

1. **Run regularly** - Run `lint:unused` before commits to keep your code clean
2. **Use prefix for intentionally unused variables** - If you need to declare variables that won't be used, prefix them with `_` (e.g., `const _unusedVar = 123;`)
3. **Fix incrementally** - For large codebases, fix one directory at a time to manage the changes better

## Configuration Details

The ESLint configuration uses:

- `eslint-plugin-unused-imports` - Plugin specifically for handling unused imports
- `@typescript-eslint/no-unused-vars` - TypeScript-aware rule for unused variables
- Custom rules to ignore variables prefixed with underscore

You can adjust the rules in `eslint.config.mjs` if you need to customize the behavior.

## Troubleshooting

If you encounter any issues:

1. Make sure you have all dependencies installed: `pnpm install`
2. Check that your ESLint configuration is properly set up
3. Try running with the `--debug` flag for more information: `pnpm run lint:fix -- --debug`

If you need to disable the rule for a specific line, you can use:

```javascript
// eslint-disable-next-line unused-imports/no-unused-imports
import { unusedImport } from 'some-module';
``` 