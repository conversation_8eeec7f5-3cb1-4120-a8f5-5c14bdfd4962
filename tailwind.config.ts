import type { Config } from "tailwindcss";

export default {
    darkMode: ["class"],
    content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/common/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
  	container: {
  		center: true,
  		padding: '2rem',
  		screens: {
  			'2xl': '1400px'
  		}
  	},
  	extend: {
		fontFamily: {
			primary: ['var(--font-nunito-sans)'],
			secondary: ['var(--font-montserrat)'],
		},
  		fontSize: {
  			'bold-48': [
  				'48px',
  				{
  					lineHeight: '64px',
  					fontWeight: '700',
  					letterSpacing: '0'
  				}
  			],
  			'bold-40': [
  				'40px',
  				{
  					lineHeight: '56px',
  					fontWeight: '700',
  					letterSpacing: '0'
  				}
  			],
  			'bold-24': [
  				'24px',
  				{
  					lineHeight: '32px',
  					fontWeight: '700',
  					letterSpacing: '0'
  				}
  			],
  			'bold-20': [
  				'20px',
  				{
  					lineHeight: '32px',
  					fontWeight: '700',
  					letterSpacing: '0'
  				}
  			],
  			'bold-18': [
  				'18px',
  				{
  					lineHeight: '28px',
  					fontWeight: '700',
  					letterSpacing: '0'
  				}
  			],
  			'bold-16': [
  				'16px',
  				{
  					lineHeight: '28px',
  					fontWeight: '700',
  					letterSpacing: '0'
  				}
  			],
  			'bold-14': [
  				'14px',
  				{
  					lineHeight: '16px',
  					fontWeight: '700',
  					letterSpacing: '0'
  				}
  			],
  			'bold-32': [
  				'32px',
  				{
  					lineHeight: '42px',
  					fontWeight: '700',
  					letterSpacing: '0'
  				}
  			],
  			'bold-12': [
  				'12px',
  				{
  					lineHeight: '15px',
  					fontWeight: '700',
  					letterSpacing: '-3%'
  				}
  			],
  			'semibold-16': [
  				'16px',
  				{
  					lineHeight: '19.2px',
  					fontWeight: '600',
  					letterSpacing: '0'
  				}
  			],
  			'semibold-24': [
  				'24px',
  				{
  					lineHeight: '28.8px',
  					fontWeight: '600',
  					letterSpacing: '0'
  				}
  			],
  			'semibold-32': [
  				'32px',
  				{
  					lineHeight: '42px',
  					fontWeight: '600',
  					letterSpacing: '0'
  				}
  			],
  			'medium-14': [
  				'14px',
  				{
  					lineHeight: '20px',
  					fontWeight: '500',
  					letterSpacing: '0'
  				}
  			],
  			'medium-16': [
  				'16px',
  				{
  					lineHeight: '28px',
  					fontWeight: '500',
  					letterSpacing: '0'
  				}
  			],
  			'medium-18': [
  				'18px',
  				{
  					lineHeight: '28px',
  					fontWeight: '500',
  					letterSpacing: '0'
  				}
  			],
  			'medium-10': [
  				'10px',
  				{
  					lineHeight: '10px',
  					fontWeight: '500',
  					letterSpacing: '0'
  				}
  			],
  			'medium-12': [
  				'12px',
  				{
  					lineHeight: '10px',
  					fontWeight: '500',
  					letterSpacing: '0'
  				}
  			],
  			'body-1': [
  				'18px',
  				{
  					lineHeight: '1.5rem',
  					letterSpacing: '0',
  					fontWeight: '400'
  				}
  			],
  			'body-2': [
  				'16px',
  				{
  					lineHeight: '20px',
  					letterSpacing: '0',
  					fontWeight: '400'
  				}
  			],
  			'regular-12': [
  				'12px',
  				{
  					lineHeight: '10px',
  					letterSpacing: '0',
  					fontWeight: '400'
  				}
  			],
  			'regular-14': [
  				'14px',
  				{
  					lineHeight: '24px',
  					letterSpacing: '0',
  					fontWeight: '400'
  				}
  			],
  			'regular-16': [
  				'16px',
  				{
  					lineHeight: '20px',
  					letterSpacing: '0',
  					fontWeight: '400'
  				}
  			],
  			'light-18': [
  				'18px',
  				{
  					lineHeight: '28px',
  					letterSpacing: '0',
  					fontWeight: '300'
  				}
  			],
  			'light-20': [
  				'20px',
  				{
  					lineHeight: '100%',
  					letterSpacing: '0',
  					fontWeight: '300'
  				}
  			]
  		},
  		colors: {
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			placeholder: '#6E717C',
  			dark: '#25292D',
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			gray: {
  				'2': 'hsla(210, 10%, 16%, 1)',
  				'4': 'hsla(227, 7%, 37%, 1)',
  				'5': 'hsla(227, 6%, 46%, 1)',
  				'6': 'hsla(228, 12%, 75%, 1)',
  				'8': 'hsla(212, 27%, 90%, 1)',
  				'10': 'hsla(218, 33%, 95%, 1)',
  				black: 'hsla(0, 0%, 0%, 1)',
				white: 'hsla(0, 0%, 100%, 1)'
  			},
  			'paymate-green': {
  				'50': 'hsla(94, 62%, 95%, 1)',
  				'600': 'hsla(96, 65%, 40%, 1)',
  				'700': 'hsla(96, 65%, 30%, 1)',
  				inactive: 'hsla(97, 97%, 86%, 1)'
  			},
  			green: {
  				'50': 'hsl(var(--green-50))',
  				'100': 'hsl(var(--green-100))',
  				'400': 'hsl(var(--green-400))',
  				'600': 'hsl(var(--green-600))',
  				'700': 'hsl(var(--green-700))',
  				'900': 'hsl(var(--green-900))'
  			},
  			orange: {
				DEFAULT: 'hsla(35, 83%, 53%, 1)',
				fainter: 'hsla(36, 100%, 93%, 1)',
  				'50': 'hsl(var(--orange-50))',
  				'200': 'hsl(var(--orange-200))',
  				'600': 'hsl(var(--orange-600))',
  				'900': 'hsl(var(--orange-900))'
  			},
  			neutral: {
  				'100': 'hsl(var(--neutral-100))',
  				'200': 'hsl(var(--neutral-200))',
  				'400': 'hsl(var(--neutral-400))',
  				'600': 'hsl(var(--neutral-600))',
  				'800': 'hsl(var(--neutral-800))',
  				'900': 'hsl(var(--neutral-900))',
  				DEFAULT: 'hsl(var(--neutral-default))'
  			},
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			},
  			sidebar: {
  				DEFAULT: 'hsl(var(--sidebar-background))',
  				foreground: 'hsl(var(--sidebar-foreground))',
  				primary: 'hsl(var(--sidebar-primary))',
  				'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
  				accent: 'hsl(var(--sidebar-accent))',
  				'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
  				border: 'hsl(var(--sidebar-border))',
  				ring: 'hsl(var(--sidebar-ring))'
  			}
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out'
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;
