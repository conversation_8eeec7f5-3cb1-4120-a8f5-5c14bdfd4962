"use client";

import { useCookies } from "@/common/hooks/useCookies";

export default function OrganizationInfo() {
  const { getOrganizationUid } = useCookies();
  const organizationUid = getOrganizationUid();

  return (
    <div className="p-4 border rounded-md shadow-sm">
      <h1 className="text-xl font-semibold mb-2">Organization Information</h1>
      {organizationUid ? (
        <div>
          <p>Organization UID: {organizationUid}</p>
          <p>The organization UID was retrieved from cookies set by middleware.</p>
        </div>
      ) : (
        <p>No organization UID found in cookies.</p>
      )}
    </div>
  );
} 