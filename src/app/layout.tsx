import type { Metada<PERSON> } from "next";
import { Nunito_Sans } from "next/font/google";
import { QueryProvider } from "@/common/providers/react-query-provider";
import { Toaster } from "@/common/components/ui/sonner";
import "@/common/styles/globals.css";
import "@/common/styles/reset-form-style.css";
import Progress from "@/common/utils/Progress";

const nunitoSans = Nunito_Sans({
  subsets: ["latin"],
  display: 'swap',
  weight: ["200", "300", "400", "500", "600", "700", "800"]
});

// Montserrat({
//   subsets: ["latin"],
//   weight: ["200", "300", "400", "500", "600", "700", "800"]
// });

export const metadata: Metadata = {
  title: "Clyppay Business",
  description: "Generated by create next app",
};


export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${nunitoSans.className} bg-background`}
      >
        <Progress>

          <QueryProvider>
            {children}

            <Toaster position="top-right" />
          </QueryProvider>

        </Progress>
      </body>
    </html>
  );
}
