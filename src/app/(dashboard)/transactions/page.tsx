"use client"

import React, { Suspense } from 'react';
import { DataTable, DataTableColumn } from '@/common/components/ui/DataTable';
import { useTable } from '@/common/hooks/useTable';
import { Label } from '@/common/components/ui/label';

// Mock data matching the screenshot
const mockTransactions = [
  {
    date: '31/10/2024',
    referenceId: 'Trc23ytssgdb',
    currency: 'NGN',
    amount: 234098.0,
    fee: 34098.0,
    type: 'Bank Transfer',
    status: 'Successful',
  },
  {
    date: '31/10/2024',
    referenceId: 'Trc23ytssgdb',
    currency: 'USD',
    amount: 234098.0,
    fee: 34098.0,
    type: 'Bank Transfer',
    status: 'Successful',
  },
  // ...repeat or generate more rows for demo
];

// Generate more rows for pagination demo
const rows = Array.from({ length: 35 }, (_, i) => ({
  ...mockTransactions[i % mockTransactions.length],
  referenceId: `Trc23ytssgdb${i + 1}`,
}));

const columns: DataTableColumn<typeof rows[0]>[] = [
  { label: 'Date', accessor: 'date', sortable: true },
  { label: 'Reference ID', accessor: 'referenceId', sortable: true },
  { label: 'Currency', accessor: 'currency', sortable: true },
  {
    label: 'Amount',
    accessor: 'amount',
    sortable: true,
    render: (row) => `${row.currency} ${row.amount.toLocaleString()}`,
  },
  {
    label: 'Fee',
    accessor: 'fee',
    sortable: true,
    render: (row) => `${row.currency} ${row.fee.toLocaleString()}`,
  },
  { label: 'Type', accessor: 'type', sortable: true },
  {
    label: 'Status',
    accessor: 'status',
    sortable: true,
    render: (row) => (
      <span className="text-green-500 font-medium">{row.status}</span>
    ),
  },
  {
    label: '',
    accessor: 'actions',
    render: () => (
      <button className="rounded-full bg-orange/10 p-2 text-orange hover:bg-orange/20">
        <span className="sr-only">Details</span>
        <svg width="16" height="16" fill="none" viewBox="0 0 24 24"><path d="M9 18l6-6-6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /></svg>
      </button>
    ),
  },
];

export default function TransactionPage() {
  const table = useTable({
    data: rows,
    initialPage: 1,
    pageSize: 8,
  });

  return (
    <Suspense>
      <div className="mx-auto space-y-4 px-6">
        <Label className='font-semibold text-xl'>All Transactions</Label>
        <DataTable
          columns={columns}
          data={table.data}
          page={table.page}
          pageSize={table.pageSize}
          total={table.total}
          onPageChange={table.setPage}
          sortBy={table.sortBy}
          sortDirection={table.sortDirection}
          onSortChange={table.onSortChange}
        />
    </div>
    </Suspense>
  );
}