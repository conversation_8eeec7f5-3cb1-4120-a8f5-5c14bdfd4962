import { BasicTableSchema } from "@/common/services/interfaces";
import { z } from "zod";
import { CreateTransferRecipientSchema, CreateWidthdrawSchema } from "./validations";

export enum TransferRecipientType {
    FIAT = 'fiat',
    CRYPTO = 'crypto',
}


export interface TTransferRecipient extends BasicTableSchema {
    type: TransferRecipientType;
    name: string;
    currency: string;
    account_number?: string;
    bank_code?: string;
    address?: string;
    network?: string;
    organization_uid: string;
    customer_uid?: string;
}

export type TCreateTransferRecipient = z.infer<typeof CreateTransferRecipientSchema>;

export type TCreateWidthdrawal = z.infer<typeof CreateWidthdrawSchema>