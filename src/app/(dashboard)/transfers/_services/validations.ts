import { z } from "zod";
import { TransferRecipientType } from "./interfaces";
import { DEFAULT_TRANSFER_SOURCE } from "./constants";


export const CreateTransferRecipientSchema = z
  .object({
    type: z.nativeEnum(TransferRecipientType),
    name: z.string().min(1, "Name is required"),
    currency: z.string().min(1, "Currency is required"),
    account_number: z.string().optional(),
    bank_code: z.string().optional(),
    address: z.string().optional(),
    network: z.string().optional(),
  })
  // .refine(
  //   (data) => {
  //     // If type is FIAT, account_number and bank_code are required
  //     if (data.type === TransferRecipientType.FIAT) {
  //       return !!data.account_number && !!data.bank_code;
  //     }
  //     // If type is CRYPTO, address and network are required
  //     if (data.type === TransferRecipientType.CRYPTO) {
  //       return !!data.address && !!data.network;
  //     }
  //     return true;
  //   },
  //   (data) => {
  //     // Return type-specific error messages
  //     if (data.type === TransferRecipientType.FIAT) {
  //       return {
  //         message: "FIAT transfers require account number and bank code.",
  //         path: ["type"],
  //       };
  //     } else {
  //       return {
  //         message: "CRYPTO transfers require address and network.",
  //         path: ["type"],
  //       };
  //     }
  //   }
  // )
  // .superRefine((data, ctx) => {
  //   if (data.type === TransferRecipientType.FIAT && !data.account_number) {
  //     ctx.addIssue({
  //       code: z.ZodIssueCode.custom,
  //       message: "Account number is required for FIAT transfers",
  //       path: ["account_number"]
  //     });
  //   }
  //   if (data.type === TransferRecipientType.CRYPTO && !data.address) {
  //     ctx.addIssue({
  //       code: z.ZodIssueCode.custom,
  //       message: "Address is required for CRYPTO transfers",
  //       path: ["address"]
  //     });   
  //   }
  //   if (data.type === TransferRecipientType.CRYPTO && !data.network) {
  //     ctx.addIssue({
  //       code: z.ZodIssueCode.custom,
  //       message: "Network is required for CRYPTO transfers",
  //       path: ["network"]
  //     });
  //   }   
  //   if (data.type === TransferRecipientType.FIAT && !data.bank_code) {
  //     ctx.addIssue({
  //       code: z.ZodIssueCode.custom,
  //       message: "Bank code is required for FIAT transfers",
  //       path: ["bank_code"]
  //     });
  //   }   
  // });

export const CreateTransferSchema = z.object({
  amount: z.number().min(1, "Amount is required"),
  currency: z.string().min(1, "Currency is required"),
  recipient: CreateTransferRecipientSchema,
});

export const CreateWidthdrawSchema = z.object({
  source: z.string().default(DEFAULT_TRANSFER_SOURCE),
  amount: z.coerce.number().positive().min(1, "Amount is required"),
  recipient: z.string().min(1, "Recipient is required"),
  reason: z.string().optional(),
  currency: z.string().default("NGN"),
})