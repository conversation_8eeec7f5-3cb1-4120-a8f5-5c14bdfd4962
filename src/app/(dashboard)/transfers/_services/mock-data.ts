import { TTransferRecipient, TransferRecipientType } from "./interfaces";

export const mockTransferRecipients: TTransferRecipient[] = [
    {
        uid: "1",
        created_at: new Date("2023-01-01T10:00:00Z").toISOString(), // Example date
        updated_at: new Date("2023-01-02T10:00:00Z").toISOString(), // Example date
        type: TransferRecipientType.FIAT,
        name: "<PERSON>",
        currency: "USD",
        account_number: "*********",
        bank_code: "XYZ123",
        address: "123 Main St, Anytown, USA",
        network: "Bank Transfer",
        organization_uid: "org-001",
        customer_uid: "cust-001",
    },
    {
        uid: "2",
        created_at: new Date("2023-02-01T10:00:00Z").toISOString(),
        updated_at: new Date("2023-02-02T10:00:00Z").toISOString(),
        type: TransferRecipientType.CRYPTO,
        name: "<PERSON>",
        currency: "BT<PERSON>",
        account_number: "1A2B3C4D5E6F7G8H9I0J",
        bank_code: undefined, 
        address: "456 Elm St, Othertown, USA",
        network: "Bitcoin Network",
        organization_uid: "org-002",
        customer_uid: "cust-002",
    },
    {
        uid: "3",
        created_at: new Date("2023-03-01T10:00:00Z").toISOString(),
        updated_at: new Date("2023-03-02T10:00:00Z").toISOString(),
        type: TransferRecipientType.FIAT,
        name: "Bob Johnson",
        currency: "EUR",
        account_number: undefined,
        bank_code: "ABC456",
        address: "789 Oak St, Sometown, USA",
        network: "Wire Transfer",
        organization_uid: "org-003",
        customer_uid: "cust-003",
    },
];
