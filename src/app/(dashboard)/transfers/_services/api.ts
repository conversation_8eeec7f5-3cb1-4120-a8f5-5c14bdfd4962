/* eslint-disable @typescript-eslint/no-explicit-any */
import { apiRequest } from "@/common/lib/api-request";
import { TResponse } from "@/common/services/interfaces";
import { getRoutes } from "@/common/services/constants";
import { TCreateTransferRecipient, TCreateWidthdrawal, TTransferRecipient } from "./interfaces";


export const GetTransferRecipients = async () =>
    await apiRequest<undefined, TResponse<TTransferRecipient[]>>({
      path: getRoutes.getTransferRecipients().path,
      method: getRoutes.getTransferRecipients().method,
    });

export const GetTransferRecipient = async (recipient_uid: string) =>
    await apiRequest<undefined, TResponse<TTransferRecipient>>({
        path: getRoutes.getTransferRecipient(recipient_uid).path,
        method: getRoutes.getTransferRecipient(recipient_uid).method,
    });

export const CreateTransferRecipient = async (data: TCreateTransferRecipient) =>
    await apiRequest<TCreateTransferRecipient, TResponse<TTransferRecipient>>({
      path: getRoutes.createTransferRecipient().path,
      method: getRoutes.createTransferRecipient().method,
      payload: data,
    });

export const CreateWidthdrawal = async (walletId: string, data: TCreateWidthdrawal) =>
    await apiRequest<TCreateWidthdrawal, TResponse<any>>({
      path: getRoutes.createWidthdrawal(walletId).path,
      method: getRoutes.createWidthdrawal(walletId).method,
      payload: data,
    });
