"use client"
import { useMutation, useQuery, UseMutationResult } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { TResponse } from "@/common/services/interfaces";
import { TCreateTransferRecipient, TCreateWidthdrawal, TTransferRecipient } from "../interfaces";
import { CreateTransferRecipient, CreateWidthdrawal, GetTransferRecipients } from "../api";
import { QUERY_KEYS } from "./query-keys";


export const useCreateRecipient = () => {
    const mutation: UseMutationResult<
        TResponse<TTransferRecipient>,
        AxiosError,
        TCreateTransferRecipient
    > = useMutation({
        mutationKey: ["create-recipient"],
        mutationFn: CreateTransferRecipient,
    });

    return {mutation}
}

export const useCreateWidthdrawal = (walletId: string) => {
    const mutation: UseMutationResult<
        // eslint-disable-next-line
        TResponse<any>,
        Axi<PERSON><PERSON><PERSON><PERSON>,
        TCreateWidthdrawal
    > = useMutation({
        mutationKey: ["create-widthdrawal"],
        mutationFn: (data: TCreateWidthdrawal) => CreateWidthdrawal(walletId, data),
    })

    return {mutation}
}

export const useGetTransferRecipients = () => {
    return useQuery({
        queryKey: [QUERY_KEYS.TRANSFER_RECIPIENTS],
        queryFn: GetTransferRecipients,
    })
}
