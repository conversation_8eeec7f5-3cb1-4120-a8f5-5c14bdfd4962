"use client"
import TransferForm from './transfer-form'
import WalletSlides from './wallet-slides'
import useGetWallet from '../../balance/swap/_hooks/use-get-wallet'
import useUniqueWallets from '../../balance/swap/_hooks/use-unique-wallets'


export default function TransferPage() {
  const { wallets, fetchingWallet } = useGetWallet()
  const uniqueWallets = useUniqueWallets(wallets)
  
  return (
    <div className="space-y-10">
      <WalletSlides wallets={uniqueWallets} loading={fetchingWallet} />
      <TransferForm wallets={uniqueWallets} loading={fetchingWallet} />
    </div>
  )
}
