import { Card, CardContent } from '@/common/components/ui/card'
import { Skeleton } from '@/common/components/ui/skeleton'

export default function WalletCardSkeleton() {
  return (
    <Card className='h-[100px] basis-full sm:basis-1/2 md:basis-1/3 shadow-none border border-gray-10 bg-[#FCFCFD]'>
        <CardContent className='p-5'>
            <div className="flex flex-col justify-between gap-y-3">
                <Skeleton className='h-5 w-40' />
                <div className="flex gap-2">
                    <Skeleton className='h-8 w-12' />
                    <Skeleton className='h-8 w-28' />
                </div>
            </div>
        </CardContent>
    </Card>
  )
}
