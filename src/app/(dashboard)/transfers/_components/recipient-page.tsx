"use client"
import ClypButton from '@/common/components/custom/clyp-button'
import Link from 'next/link'
import React from 'react'
import RecipientDatatable from './recipient-datatable'
import { useGetTransferRecipients } from '../_services/queries/queries'

export default function RecipientPage() {
  const {data: response, isLoading} = useGetTransferRecipients()
  return (
    <div className="py-4 px-4">
      <header className='flex items-center justify-between gap-4'>
        <h1 className="text-bold-20">{response?.data?.length || 0} Recipients</h1>
        <Link href={"/transfers/new-recipient"}>
          <ClypButton
            text="Add recipient"
            className="w-fit bg-orange text-white shadow-none"
          />
        </Link>
      </header>

      <RecipientDatatable recipients={response?.data || []} loading={isLoading} />
    </div>
  )
}
