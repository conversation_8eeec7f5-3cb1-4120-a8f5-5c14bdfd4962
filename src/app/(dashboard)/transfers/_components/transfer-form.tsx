"use client"
import { Form, FormControl, FormField, FormItem } from "@/common/components/ui/form";
import { useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { CreateWidthdrawSchema } from "../_services/validations";
import { TCreateWidthdrawal, TTransferRecipient } from "../_services/interfaces";
import PlaceholderFloatInput from "@/common/components/custom/placeholder-float-input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/common/components/ui/select";
import { TWallet } from "../../balance/_services/interfaces";
import { useState } from "react";
import { useCreateWidthdrawal, useGetTransferRecipients } from "../_services/queries/queries";
import { toastErrorMessage } from "@/common/lib/toast-message";
import PreviewTransfer from "./preview-transfer";
import { Input } from "@/common/components/ui/input";
import { cn } from "@/common/lib/utils";
import TransferFormLoader from "./transfer-form-loader";


const DEFAULT_TRANSFER_SOURCE = '';

const DEFAULT_VALUES = {
    currency: '',
    source: DEFAULT_TRANSFER_SOURCE,
    recipient: '',
    reason: '',
}

export default function TransferForm({wallets, loading}: {wallets: TWallet[], loading: boolean}) {
    
    const [selectedWallet, setSelectedWallet] = useState<TWallet | null>(null);
    const {mutation} = useCreateWidthdrawal(selectedWallet?.uid || '');
    const [targetRecipient, setTargetRecipient] = useState<TTransferRecipient | null>(null);

    const {data: recipientsResponse, isLoading: loadingRecipients} = useGetTransferRecipients()

    const recipients = recipientsResponse?.data || []

    
    const form = useForm<TCreateWidthdrawal>({
        resolver: zodResolver(CreateWidthdrawSchema),
        defaultValues: DEFAULT_VALUES
    })

    const onSubmit = (data: TCreateWidthdrawal) => {
        if(!selectedWallet) {
            toastErrorMessage('Please select a wallet');
            return
        };
        mutation.mutate(data);
    }

    const amount = useWatch({
        control: form.control,
        name: 'amount'
    })

    
    if(loading) {
        return <TransferFormLoader />
    }


    return (
        <div className="space-y-5">
            <h1 className="text-bold-20">Select transfer Details</h1>

            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)}>
                    <main className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <FormField
                                control={form.control}
                                name="recipient"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormControl>  
                                            <Select 
                                                value={field.value} 
                                                onValueChange={(value) => {
                                                    field.onChange(value);
                                                    setTargetRecipient(recipients.find((recipient) => recipient.uid === value) || null);
                                                }}
                                            >
                                                <SelectTrigger className="w-full h-[52px] md:h-[64px] rounded-full">
                                                    <SelectValue 
                                                        placeholder={loadingRecipients? 'Please wait' : "Select recipient"} 
                                                    />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {recipients.map((recipient) => (
                                                        <SelectItem key={recipient.uid} value={recipient.uid}>
                                                            {recipient.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </FormControl>
                                    </FormItem>
                                )}
                            />
                        </div>
                        <div className="clear-disabled-input">
                            <Input
                                value={targetRecipient?.name}
                                disabled={true}
                                className={cn(
                                    "w-full h-[52px] md:h-[64px] rounded-full outline-none"
                                )}
                                placeholder="Full name"
                            />
                        </div>
                        <div className="clear-disabled-input">
                            <Input
                                value={targetRecipient?.currency}
                                disabled={true}
                            className={cn(
                                "clear-disabled-input",
                                "w-full h-[52px] md:h-[64px] rounded-full outline-none"
                            )}
                                placeholder="Coin/Currency"
                            />
                        </div>
                        <div className="clear-disabled-input">
                            <Input
                                value={targetRecipient?.account_number}
                                disabled={true}
                            className={cn(
                                "clear-disabled-input",
                                "w-full h-[52px] md:h-[64px] rounded-full outline-none"
                            )}
                                placeholder="Account Number/Wallet Address"
                            />
                        </div>
                        <FormField
                            control={form.control}
                            name="amount"
                            render={({ field }) => (
                                <FormItem>
                                    <FormControl>  
                                        <PlaceholderFloatInput
                                            field={field}
                                            name={field.name}
                                            placeholder="Amount"
                                        />
                                    </FormControl>
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="currency"
                            render={({ field }) => (
                                <FormItem>
                                    <FormControl>  
                                        <Select 
                                            value={field.value} 
                                            onValueChange={(value) => {
                                                field.onChange(value);
                                                setSelectedWallet(wallets.find((wallet) => wallet.currency === value) || null);
                                            }}
                                        >
                                            <SelectTrigger className="w-full h-[52px] md:h-[64px] rounded-full">
                                                <SelectValue 
                                                    placeholder={loadingRecipients? 'Please wait' : "Transfer From"} 
                                                />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {wallets.map((wallet) => (
                                                    <SelectItem key={wallet.uid} value={wallet.currency}>
                                                        {wallet.currency}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </FormControl>
                                </FormItem>
                            )}
                        />
                    </main>
                </form>
            </Form>
            <TransferAmount wallet={selectedWallet} amount={amount} />
            <PreviewTransfer
                recipient={targetRecipient}
                amount={amount}
                wallet={selectedWallet}
                disabled={mutation.isLoading}
                submit={form.handleSubmit(onSubmit)}
                isLoading={mutation.isLoading}
            />
        </div>
    )
}


const TransferAmount = ({wallet, amount}: {wallet: TWallet | null, amount: number}) => {
    return (
        <>
            {wallet && amount > 0 && (
                <p className="text-semibold-20 text-gray-2">
                    {wallet.currency} {parseFloat(String(amount)).toFixed(2)} will be sent
                </p>
            )}
        </>
    )
}