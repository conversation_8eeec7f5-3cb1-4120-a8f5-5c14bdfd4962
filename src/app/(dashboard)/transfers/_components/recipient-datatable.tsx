import { Table, TableCell, TableBody, TableHead, TableHeader, TableRow } from '@/common/components/ui/table'
// import { Button } from '@/common/components/ui/button'
// import { Icons } from '@/common/components/custom/icon-templates'
import { formatDate } from '@/common/lib/utils'
import { TTransferRecipient } from '../_services/interfaces'

export default function RecipientDatatable({recipients, loading}: {recipients: TTransferRecipient[], loading: boolean}) {

    const renderRows = (recipients: TTransferRecipient[]) => {
        return <>
            {recipients.map((recipient) => (
                <TableRow key={recipient.uid}>
                    <TableCell>{recipient.name}</TableCell>
                    <TableCell>{recipient.currency}</TableCell>
                    <TableCell>{formatDate(recipient.created_at)}</TableCell>
                </TableRow>
            ))
        }
        </>
    }

    const renderLoading = () => {
        return (
            <TableRow>
                <TableCell colSpan={4} className='text-center'>Loading...</TableCell>
            </TableRow>
        )
    }

    return (
        <div className='mt-4 p-2 border rounded'>
            <Table>
            <TableHeader className='bg-neutral-100'>
                <TableRow className='border-none'>
                    <TableHead className='text-left text-gray-2 text-semibold-14'>
                        <span>RecipientName</span>
                    </TableHead>
                    <TableHead className='text-gray-2 text-semibold-14'>
                        <span>Currency / Coin</span>
                    </TableHead>
                    <TableHead className='text-gray-2 text-semibold-14'>
                        <span>Date Added</span>
                    </TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                {loading ? renderLoading() : renderRows(recipients)}
            </TableBody>
        </Table>
    </div>
  )
}
