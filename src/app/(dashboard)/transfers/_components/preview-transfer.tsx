"use client"
import { useState } from 'react'
import ClypButton from '@/common/components/custom/clyp-button';
import { DialogContent, DialogTrigger } from '@/common/components/ui/dialog';
import { Dialog } from '@/common/components/ui/dialog';
import BackButton from '@/common/components/custom/back-button';
import { Table, TableBody, TableCell, TableRow } from '@/common/components/ui/table';
import EllipsisLoader from '@/common/components/custom/ellipsis-loader';
import { TCreateTransferRecipient } from '../_services/interfaces';
import { TWallet } from '../../balance/_services/interfaces';
import TransferConversion from './transfer-conversion';


interface TPreviewTransferProps {
    disabled?: boolean, 
    isLoading?: boolean,
    recipient: TCreateTransferRecipient | null,
    amount: number,
    wallet: TWallet | null,
    submit: () => void
}

export default function PreviewTransfer({
    disabled,
    isLoading,
    recipient,
    amount,
    wallet,
    submit
}: TPreviewTransferProps) {
    const [isOpen, setIsOpen] = useState(false);

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <ClypButton 
                    classes="bg-gray-2 shadow-none h-[50px] min-w-[200px] outline-none text-white"
                    text="Preview conversion"
                    type="button"
                    disabled={disabled}
                    isLoading={isLoading}
                />
            </DialogTrigger>
            <DialogContent 
                className="max-w-[450px]"
                onEscapeKeyDown={(e) => e.preventDefault()}
                onPointerDownOutside={(e) => e.preventDefault()}
            >
                {isLoading ? (
                    <div className='h-[400px] flex justify-center items-center'>
                        <EllipsisLoader />
                    </div>
                ):(
                    <section className='pt-10 flex flex-col items-center gap-7'>
                        <div className="flex items-center gap-4 w-full px-3">
                            <BackButton action={() => setIsOpen(false)} hideText className='w-[48px] h-[48px]' />
                            <h3 className='text-semibold-24'>Confirm transfer</h3>
                        </div>
                        <div className="space-y-1 text-center w-full px-3">
                            {
                                isLoading ? (
                                    <div className='flex justify-center items-center h-full'>
                                        <EllipsisLoader />
                                    </div>
                                ) : (
                                    <Table>
                                        <TableBody>
                                            <TableRow className='border-none'>
                                                <TableCell className='py-3 text-left text-regular-16 text-gray-4'>Recipient</TableCell>
                                                <TableCell className='py-3 text-right text-semibold-16 text-gray-2'>{recipient?.name}</TableCell>
                                            </TableRow>
                                            <TableRow className='border-none'>
                                                <TableCell className='py-3 text-left text-regular-16 text-gray-4'>Sending from</TableCell>
                                                <TableCell className='py-3 text-right text-semibold-16 text-gray-2'>
                                                    {wallet?.currency} Wallet
                                                </TableCell>
                                            </TableRow>
                                            <TableRow className='border-none'>
                                                <TableCell className='py-3 text-left text-regular-16 text-gray-4'>Receiving in</TableCell>
                                                <TableCell className='py-3 text-right text-semibold-16 text-gray-2'>
                                                    {recipient?.currency} Bank Account - {recipient?.account_number}
                                                </TableCell>
                                            </TableRow>
                                            <TableRow className='border-none'>
                                                <TableCell className='py-3 text-left text-regular-16 text-gray-4'>Amount to Send</TableCell>
                                                <TableCell className='py-3 text-right text-semibold-16 text-gray-2'>{wallet?.currency} {amount}</TableCell>
                                            </TableRow>
                                            {/* Show tranfer conversion on different currencies */}
                                            {wallet?.currency !== recipient?.currency && (
                                                <TransferConversion />
                                            )}
                                        </TableBody>
                                    </Table>
                                )
                            }
                        </div>
                        <div className="flex flex-col items-center gap-5">
                            <ClypButton 
                                classes="bg-orange w-full shadow-none h-[50px] min-w-[200px] outline-none text-white"
                                text="Confirm transfer"
                                onClick={submit}
                                isLoading={isLoading}
                                disabled={disabled}
                            />
                        </div>
                    </section>
                )}
            </DialogContent>
        </Dialog>
    )
}
