"use client"
import WalletCard from "./wallet-card";
import WalletCardSkeleton from "./wallet-card-skeleton";
import { TWallet } from "../../balance/_services/interfaces";

export default function WalletSlides({wallets, loading}: {wallets: TWallet[], loading: boolean}) {
  

  if(loading) return (
    <div className="flex gap-4 py-10">
      {Array.from({ length: 3 }).map((_, index) => (
        <WalletCardSkeleton key={index} />
      ))}
    </div>
  )

  return (
    <div className="flex gap-4 py-10">
      {wallets.map((wallet) => (
        <WalletCard key={wallet.uid} wallet={wallet} />
      ))}
    </div>
  )
}
