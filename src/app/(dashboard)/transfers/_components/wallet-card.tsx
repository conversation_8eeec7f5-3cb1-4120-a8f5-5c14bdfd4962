import { Card, CardContent } from '@/common/components/ui/card'
import { TWallet } from '../../balance/_services/interfaces'

export default function WalletCard({wallet}: {wallet: TWallet}) {
  return (
    <Card className='h-[100px] basis-full sm:basis-1/2 lg:basis-1/3 pb-0 border-gray-10 shadow-none border bg-[#FCFCFD]'>
        <CardContent className='p-5'>
            <div className="flex flex-col justify-between gap-y-3">
                <p className='montserrat text-gray-4 text-regular-12'>
                    Total {wallet.currency} Balance
                </p>
                <h1 className="mt-2 text-bold-24 text-gray-2">
                    {wallet.currency} {wallet.balance}
                </h1>
            </div>
        </CardContent>
    </Card>
  )
}
