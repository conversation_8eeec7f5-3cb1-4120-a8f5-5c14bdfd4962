"use client"
import PlaceholderFloatInput from '@/common/components/custom/placeholder-float-input'
import { useForm } from 'react-hook-form'
import { TCreateTransferRecipient, TransferRecipientType } from '../_services/interfaces'
import { zodResolver } from '@hookform/resolvers/zod'
import { CreateTransferRecipientSchema } from '../_services/validations'
import { Form, FormControl, FormItem, FormField, FormMessage } from '@/common/components/ui/form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/common/components/ui/select'
import { TransferCurrencyMap } from '../_services/constants'
import { useFetchCurrencies } from '../../balance/_services/queries/queries'
import { WalletTypeEnum } from '../../balance/[walletId]/history/_services/interfaces'
import { useEffect, useMemo } from 'react'
import ClypButton from '@/common/components/custom/clyp-button'
import { useCreateRecipient } from '../_services/queries/queries'
import { toastErrorMessage, toastSuccessMessage } from '@/common/lib/toast-message'
import useResolveBankAccount from '../_hooks/use-resolve-bank-account'
import useBankList from '../_hooks/use-bank-list'
import useNetworkList from '../_hooks/use-network-list'
import { cn } from '@/common/lib/utils'


// Map TransferRecipientType to WalletTypeEnum
const selectedTypeMap = {
    [TransferRecipientType.FIAT]: WalletTypeEnum.FIAT,
    [TransferRecipientType.CRYPTO]: WalletTypeEnum.CRYPTO,
}

const DEFAULT_VALUES = {
    type: TransferRecipientType.FIAT,
    name: '',
    currency: '',
    account_number: '',
    bank_code: '',
    address: '',
    network: '',
}

export default function NewRecipientForm() {

    const {mutation} = useCreateRecipient()

    const form = useForm<TCreateTransferRecipient>({
        resolver: zodResolver(CreateTransferRecipientSchema),
        defaultValues: DEFAULT_VALUES,
        mode: 'onChange'
    })

    // Watch for type changes
    const recipientType = form.watch('type');
    const accountNumber = form.watch('account_number')
    const bankCode = form.watch('bank_code')
    const currency = form.watch('currency')

    const isFiat = recipientType === TransferRecipientType.FIAT;

    const {banks, loading: loadingBanks} = useBankList(currency)
    const {networks, loading: loadingNetworks} = useNetworkList(!isFiat, currency)

    const {
        resolvedAccount,
        loading: resolvingAccount,
        resolveAccount,
        clearResolvedAccount
    } = useResolveBankAccount(
        accountNumber || '',
        bankCode || ''
    )

    // const formState = form.watch();

    useEffect(() => {
        if (currency !== 'NGN' && recipientType === TransferRecipientType.FIAT) {
            clearResolvedAccount();
            form.setValue('name', '');
        }
    }, [currency, recipientType, clearResolvedAccount, form]);

    useEffect(() => {
        if(resolvedAccount) {
            form.setValue('name', resolvedAccount.account_name)
        }
    }, [resolvedAccount, form])


    const walletType = selectedTypeMap[recipientType] ?? WalletTypeEnum.FIAT;


    useEffect(() => {
        if (isFiat) {
            form.setValue('address', DEFAULT_VALUES.address);
            form.setValue('network', DEFAULT_VALUES.network);
        } else {
            form.setValue('account_number', DEFAULT_VALUES.account_number);
            form.setValue('bank_code', DEFAULT_VALUES.bank_code);
        }

        if(form.getValues('name')) {
            form.setValue('name', '')
        }
    }, [isFiat, form]);

    const {data: response, isLoading: loadingCurrencies} = useFetchCurrencies(walletType, true);

    const currencies = useMemo(() => {
        return response?.data || []
    }, [response])

    // Only reset when response first changes
    useEffect(() => {
        if (response) {
            form.setValue('currency', '')
        }
    }, [response, form])

    const onSubmitHandler = async (data: TCreateTransferRecipient) => {
        await mutation.mutateAsync(data).then(() => {

            toastSuccessMessage('Recipient created successfully')
            form.reset(DEFAULT_VALUES)

        }).catch((err) => {

            toastErrorMessage(err.response?.data.message || "Unable to create recipient, please try again later")

        })
    }

    const isFormValid = () => {
        const values = form.getValues();

        if (!values.type || !values.currency) {
            return false;
        }

        if (isFiat) {
            return !!values.bank_code && !!values.account_number && !!values.name;
        } else {
            // Validate CRYPTO-specific fields
            return !!values.address && !!values.network && !!values.name;
        }
    };

    return (
        <div className='max-w-3xl'>
            <Form {...form}>

                <form onSubmit={form.handleSubmit(onSubmitHandler)}>
                    <main className='grid grid-cols-2 gap-5'>
                        <FormField
                            control={form.control}
                            name="type"
                            render={({ field }) => (
                                <FormItem>
                                    <FormControl>
                                        <Select
                                            value={field.value}
                                            onValueChange={field.onChange}
                                        >
                                            <SelectTrigger className='w-full h-[56px] md:h-[60px] rounded-full'>
                                                <SelectValue placeholder='Select Type' />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Object.entries(TransferCurrencyMap).map(([key, value]) => (
                                                    <SelectItem key={key} value={key}>{value}</SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="currency"
                            disabled={loadingCurrencies}
                            render={({ field }) => (
                                <FormItem>
                                    <FormControl>
                                        <Select
                                            value={field.value}
                                            onValueChange={field.onChange}
                                        >
                                            <SelectTrigger className='w-full h-[56px] md:h-[60px] rounded-full'>
                                                <SelectValue placeholder={loadingCurrencies?'Loading...':'Select Currency'} />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {currencies.map((currency) => (
                                                    <SelectItem key={currency.uid} value={currency.currency}>
                                                        {currency.currency}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        {/* FIAT-specific fields */}
                        {isFiat && (
                            <>
                                <FormField
                                    control={form.control}
                                    name="bank_code"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormControl>
                                                <Select
                                                    value={field.value}
                                                    onValueChange={field.onChange}
                                                >
                                                    <SelectTrigger className='w-full h-[56px] md:h-[60px] rounded-full'>
                                                        <SelectValue placeholder={loadingBanks ? 'Loading Banks...' : 'Select Bank'}>
                                                            {banks.find(bank => bank.code === field.value)?.name}
                                                        </SelectValue>
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {loadingBanks ? (
                                                            <p className='text-sm text-gray-500 text-center p-3'>Loading...</p>
                                                        ) : (
                                                            banks.map((bank) => (
                                                                <SelectItem key={bank.id} value={bank.code}>{bank.name}</SelectItem>
                                                            ))
                                                        )}
                                                    </SelectContent>
                                                </Select>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="account_number"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormControl>
                                                <PlaceholderFloatInput
                                                    field={field}
                                                    name='account_number'
                                                    placeholder={'Account Number'}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                            </>
                        )}

                        {/* CRYPTO-specific fields */}
                        {!isFiat && (
                            <>
                                <FormField
                                    control={form.control}
                                    name="network"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormControl>
                                                <Select
                                                    value={field.value}
                                                    onValueChange={field.onChange}
                                                    disabled={loadingNetworks}
                                                >
                                                    <SelectTrigger className='w-full h-[56px] md:h-[60px] rounded-full'>
                                                        <SelectValue
                                                            placeholder={loadingNetworks ? 'Loading Networks...' : 'Select Network'}
                                                        />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {!networks?.length || networks.length === 0 ? (
                                                            <p className='text-sm text-gray-500 text-center p-3'>No networks found</p>
                                                        ) : (
                                                            networks.map((network) => (
                                                                <SelectItem key={network} value={network}>{network}</SelectItem>
                                                            ))
                                                        )}
                                                    </SelectContent>
                                                </Select>
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="address"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormControl>
                                                <PlaceholderFloatInput
                                                    placeholder='Address'
                                                    field={field}
                                                    name='address'
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </>
                        )}

                        <div className={cn(
                            "col-span-2",
                            isFiat? resolvedAccount? "block":"hidden" : "block"
                        )}>
                            <FormField
                                control={form.control}
                                name="name"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormControl>
                                            <PlaceholderFloatInput
                                                placeholder='Full Name'
                                                field={field}
                                                name='name'
                                                disabled={isFiat || resolvingAccount}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                    </main>

                    <div className="mt-5">
                        {isFiat && currency && accountNumber && bankCode && !resolvedAccount ? (
                            <ClypButton
                                type="button"
                                text="Resolve Account"
                                className="bg-gray-2 text-white md:h-[50px]"
                                isLoading={resolvingAccount}
                                onClick={resolveAccount}
                            />
                        ) : (
                            <ClypButton
                                type="submit"
                                text="Save Recipient"
                                className="bg-gray-2 text-white md:h-[50px]"
                                disabled={!isFormValid()}
                                isLoading={mutation.isLoading}
                            />
                        )}
                    </div>
                </form>
            </Form>
        </div>
    )
}

