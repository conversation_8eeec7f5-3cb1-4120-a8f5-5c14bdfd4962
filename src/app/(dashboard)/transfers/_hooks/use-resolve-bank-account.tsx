"use client"
import { useCallback, useState } from "react"
import { toastErrorMessage } from "@/common/lib/toast-message"

import { ResolveAccount } from "@/common/services/api"
import { TResolveAccountData } from "@/common/services/interfaces"

export default function useResolveBankAccount(accountNumber: string, bankCode: string) {
    const [resolvedAccount, setResolvedAccount] = useState<TResolveAccountData | null>(null)
    const [isLoading, setIsLoading] = useState(false)

    const resolveAccount = useCallback(async() => {
        if(!accountNumber || !bankCode) return
        try {
            setIsLoading(true)
            
            const response = await ResolveAccount({
                account_number: String(accountNumber),
                bank_code: String(bankCode)
            })
            
            setResolvedAccount(response.data)
        }catch {
            toastErrorMessage("Account details confirmation failed")
        }finally {
            setIsLoading(false)
        }
    }, [accountNumber, bankCode])

    const clearResolvedAccount = useCallback(() => {
        setResolvedAccount(null)
    }, [])

    return {resolvedAccount, loading: isLoading, resolveAccount, clearResolvedAccount}
}