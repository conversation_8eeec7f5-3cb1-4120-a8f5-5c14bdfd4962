"use client"
import { useCallback, useEffect, useState } from "react"
import { toastErrorMessage } from "@/common/lib/toast-message"

import { GetNGNBanks } from "@/common/services/api"
import { TNGNBanks } from "@/common/services/interfaces"

export default function useBankList(currency: string) {
    const [banks, setBanks] = useState<TNGNBanks[]>([])
    const [isLoading, setIsLoading] = useState(false)

    const getBankList = useCallback(async() => {
        try {
            setIsLoading(true)
            
            const response = await GetNGNBanks()
            setBanks(response.data)

        }catch {
            toastErrorMessage("Bank list fetch failed")
        }finally {
            setIsLoading(false)
        }
    }, [])

    useEffect(() => {
        if(currency === 'NGN') {
            getBankList()
        }
    }, [getBankList, currency])

    return {banks, loading: isLoading}
}