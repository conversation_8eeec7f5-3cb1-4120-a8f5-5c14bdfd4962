"use client"
import { useCallback, useEffect, useState } from "react"
import { toastErrorMessage } from "@/common/lib/toast-message"

import { GetNetworkList } from "@/common/services/api"

export default function useNetworkList(isCrypto: boolean, currency: string) {
    const [networks, setNetworks] = useState<string[]>([])
    const [isLoading, setIsLoading] = useState(false)

    const getNetworkList = useCallback(async() => {
        try {
            setIsLoading(true)

            const response = await GetNetworkList(currency)
            setNetworks(response.data)
        }catch {
            toastErrorMessage("Network list fetch failed")
        }finally {
            setIsLoading(false)
        }
    }, [currency])

    useEffect(() => {
        if(isCrypto) {
            getNetworkList()
        }
    }, [getNetworkList, currency, isCrypto])

    return {networks, loading: isLoading}
}
