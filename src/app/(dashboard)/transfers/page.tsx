import React, { Suspense } from 'react'
import LoadingWallet from '../balance/swap/_components/loading-wallet'
import CustomTabs from '@/common/components/custom/custom-tabs'
import TransferPage from './_components/transfer-page'
import RecipientPage from './_components/recipient-page'

export default async function Transfer() {
  return (
    <section className="container">
      <Suspense fallback={<LoadingWallet />}>
        <CustomTabs
          tabs={[
            {
              label: 'Make a transfer',
              value: 'transfer',
              content: <TransferPage />,
            },
            {
              label: 'Manage recipients',
              value: 'recipient',
              content: <RecipientPage />,
            },
          ]}
          defaultValue="transfer"
          property="type"
        />
      </Suspense>
    </section>
  )
}
