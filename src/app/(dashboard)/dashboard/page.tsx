/* eslint-disable unused-imports/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
"use client"
import React, { useEffect, useState } from 'react'
import CompleteProfileNotice from './_components/complete-profile-notice'
import { useGetProfile } from '@/app/(auth)/_services/queries/queries'
import useOrganizationStore from '@/common/store/use-organization-store'
import { Bell, CalendarIcon, LoaderPinwheel } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/common/components/ui/card';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/common/components/ui/select";
import { DateRangePicker } from '@/common/hooks/date-range-picker'

export default function Dashboard() {
  const { data: profileData, isLoading } = useGetProfile()
  const { setOrganization, setAccount } = useOrganizationStore()

  console.log("profileData", profileData)

  const [currency, setCurrency] = useState("NGN");
  const [dateRange, setDateRange] = useState({ from: "Feb 07, 2025", to: "Feb 13, 2025" });
  const [period, setPeriod] = useState("Last 7 Days");

  // Update organization data when dashboard loads
  useEffect(() => {
    if (profileData?.data) {
      setAccount(profileData.data.account)
      setOrganization(profileData.data.organization)
    }
  }, [profileData, setOrganization, setAccount])

  if (isLoading) {
    return (
      <div className="min-h-[60vh] flex items-center justify-center">
        <LoaderPinwheel className="size-24 animate-spin text-paymate-green-50" />
      </div>
    )
  }

  // Mock data for summary and chart
  const summaryData = [
    {
      title: "Total Transaction Recieved",
      value: "NGN 500,000",
      count: "1,250",
    },
    {
      title: "Total Payout",
      value: "NGN 500,000",
      count: "750",
    },
    {
      title: "Total Transfer",
      value: "NGN 500,000",
      count: "300",
    },
  ];
  const chartData = [
    { day: "Feb 07", value: 100000 },
    { day: "Feb 08", value: 250000 },
    { day: "Feb 09", value: 300000 },
    { day: "Feb 10", value: 150000 },
    { day: "Feb 11", value: 400000 },
    { day: "Feb 12", value: 200000 },
    { day: "Feb 13", value: 500000 },
  ];


  return (
    <div>
      <CompleteProfileNotice />
      {/* Dashboard summary and chart section below */}
      <div className="px-4 md:px-10 py-8 w-full max-w-7xl mx-auto">
        {/* Transaction Summary Cards */}

        <div className="w-full flex justify-end">

          <div className="flex items-center gap-4">

            <span className="cursor-pointer inline-flex items-center gap-1 rounded-md border px-4 py-1 bg-white shadow-sm">
              <span className="w-2 h-2 rounded-full bg-green-500 inline-block" />
              <span className="font-medium text-green-700">Live</span>
            </span>

            <span className='cursor-pointer'>
              <Bell />
            </span>

          </div>
        </div>

        <div className="flex justify-end items-center my-4 gap-4">
          <div className="flex gap-3 items-center">
            <button className="rounded-full border px-4 py-2 text-sm font-medium bg-white shadow-sm">{period}</button>

            {/* <input type="text" value={dateRange.from} readOnly className="rounded-full border px-4 py-2 text-sm w-36 text-center" />
            <input type="text" value={dateRange.to} readOnly className="rounded-full border px-4 py-2 text-sm w-36 text-center" />
            <button className="rounded-full bg-orange text-white px-8 py-2 font-semibold ml-2">Filter</button> */}

            <DateRangePicker
              onUpdate={(values) => console.log(values)}
              initialDateFrom="2023-01-01"
              initialDateTo="2023-12-31"
              align="start"
              locale="en-GB"
              showCompare={false}
            />

          </div>
        </div>



        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-semibold">Transaction Summary</h2>

          <Select value={currency} onValueChange={setCurrency}>

            <SelectTrigger className="w-[140px] text-xs rounded-[10px] h-[36px] leading-[1.5em]" aria-label="Select a value">
              <CalendarIcon size={12} />
              <SelectValue placeholder="Select a range" />
            </SelectTrigger>
            <SelectContent className="rounded-xl text-xs">
              <SelectItem value="NGN" className="rounded-lg text-xs">
                NGN
              </SelectItem>
              <SelectItem value="USD" className="rounded-lg text-xs">
                USD
              </SelectItem>
            </SelectContent>
          </Select>

        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
          {summaryData.map((item, idx) => (
            <Card key={idx} className="rounded-2xl border border-gray-100 bg-white dark:bg-background text-black">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium mb-2">{item.title}</CardTitle>
                <div className="text-2xl font-semibold">{item.value}</div>
              </CardHeader>
              <CardContent className="pt-0 text-sm font-semibold">{item.count}</CardContent>
            </Card>
          ))}
        </div>
        {/* Chart Section */}
        <div className="bg-white rounded-2xl border border-gray-100 p-6 mt-8">
          <ResponsiveContainer width="100%" height={350}>
            <AreaChart data={chartData} margin={{ top: 20, right: 30, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis dataKey="day" tick={{ fontSize: 14 }} />
              <YAxis tick={{ fontSize: 14 }} tickFormatter={v => v.toLocaleString()} label={{ value: "Days", angle: -90, position: "insideLeft", fontSize: 16 }} />
              <Tooltip formatter={v => v.toLocaleString()} />
              <Area
                type="monotone"
                dataKey="value"
                stroke="#FFA500"
                fill="#FFF7E6"
                strokeWidth={2}
                dot={{ stroke: "#FFA500", strokeWidth: 2, fill: "#FFA500", r: 5 }}
                activeDot={{ r: 7 }}
              />
            </AreaChart>
          </ResponsiveContainer>
          <div className="text-center text-gray-700 font-medium mt-2">Transaction Value (NGN 15Million)</div>
        </div>
      </div>
    </div>
  )
}
