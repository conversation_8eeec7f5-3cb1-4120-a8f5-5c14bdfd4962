"use client"
import { REGU<PERSON>TORY_COMPLIANCE_STATUS } from '@/app/create-organization/_services/interfaces'
import { Card, CardDescription, CardHeader, CardTitle } from '@/common/components/ui/card'
import useOrganizationStore from '@/common/store/use-organization-store'
import Link from 'next/link'

export default function CompleteProfileNotice() {
  const { organization } = useOrganizationStore()

  const compliance_status = {
    [REGULATORY_COMPLIANCE_STATUS.SUBMITTED_FOR_REVIEW]: {
      text: `Your business registration & license documents has been submitted for review`,
      trigger: null,
    },
    [REGULATORY_COMPLIANCE_STATUS.IN_REVIEW]: {
      text: `Your business registration & license documents is in review`,
      trigger: null,
    },
    [REGULATORY_COMPLIANCE_STATUS.NON_COMPLIANT]: {
      text: `Complete your profile by uploading your business registration & license documents.`,
      trigger: {text: 'Get Started', href: '/complete-profile'},
    },
    undefined: {
      text: `Complete your profile by uploading your business registration & license documents.`,
      trigger: {text: 'Get Started', href: '/complete-profile'},
    },
  }

  if(!organization || compliance_status.hasOwnProperty(organization.regulatory_compliance_status)) {

    const feedback = compliance_status[
      organization?.regulatory_compliance_status as keyof typeof compliance_status
    ];

    return (
      <Card className='border-none rounded-none shadow-none bg-gray-8 text-gray-black'>
          <CardHeader className='p-5 text-center'>
              <CardTitle className="text-bold-12!">
              Welcome to Paymate
              </CardTitle>
              <CardDescription className='text-medium-10'>
                {feedback.text} {" "}
                {feedback.trigger && (
                  <Link href={feedback.trigger.href} className='text-paymate-green-600'>
                  {feedback.trigger.text}
                  </Link>
                )}
              </CardDescription>
          </CardHeader>
      </Card>
    )
  }

  return null
}
