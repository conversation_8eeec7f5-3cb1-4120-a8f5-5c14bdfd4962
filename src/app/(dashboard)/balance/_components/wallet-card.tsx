"use client"
import ClypButton from '@/common/components/custom/clyp-button'
import { Card, CardDescription } from '@/common/components/ui/card'
import { CardContent } from '@/common/components/ui/card'
// import { useRouter } from 'next/navigation'
import { WalletTabEnums } from '../_services/enums'
import { TWallet } from '../_services/interfaces'
import { WalletTypeEnum } from '../[walletId]/history/_services/interfaces'
import Link from 'next/link'

interface WalletCardProps {
    currencyType: WalletTypeEnum,
    wallet: TWallet,
    tab: WalletTabEnums
}

export default function WalletCard({ currencyType, wallet, tab }: WalletCardProps) {
    // const router = useRouter();

    const isFiat = currencyType === WalletTypeEnum.FIAT;

    const navigateTo = {
        [WalletTabEnums.BALANCES]: `/balance/${wallet.uid}/fund`,
        [WalletTabEnums.SWAP]: `/balance/swap?wallet=${wallet.uid}`
    }
    const navigateText = {
        [WalletTabEnums.BALANCES]: "Fund Balance",
        [WalletTabEnums.SWAP]: "Convert"
    }

    return (
        <Card className="shadow-lg rounded-2xl border-[0.5px]">
            <CardContent className='space-y-4 text-regular-16 text-gray-2 pt-4'>
                <p className="">{wallet.currency}</p>

                <div className='space-y-1'>
                    <CardDescription>
                        Available {isFiat ? "Fiat" : "Crypto"} balance
                    </CardDescription>
                    <h1 className='text-bold-32'>
                        {wallet.currency}{
                            Number(wallet.balance) > 0 ?
                                `${" "}${Number(wallet.balance).toLocaleString()}`
                                : `${" "} 0.00`
                        }
                    </h1>
                </div>

                <div className='flex w-full justify-between gap-2'>
                    <Link href={navigateTo[tab]} className='w-full'>
                        <ClypButton
                            classes="shadow-none bg-gray-2 h-[40px] text-medium-18 text-white w-full"
                        >
                            {navigateText[tab]}
                        </ClypButton>
                    </Link>

                    <Link href={`/balance/${wallet.uid}/history`} className='w-full'>
                        <ClypButton
                            variant="outline"
                            classes="shadow-none h-[40px] text-medium-18 text-orange w-full hover:bg-initial"
                        >
                            View History
                        </ClypButton>
                    </Link>

                </div>
            </CardContent>
        </Card>
    )
}
