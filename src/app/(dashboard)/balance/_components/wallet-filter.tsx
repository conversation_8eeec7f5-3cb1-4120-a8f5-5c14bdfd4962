"use client"
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue,
} from "@/common/components/ui/select"
import { Input } from '@/common/components/ui/input'
import { Search } from 'lucide-react'
import NewWalletModal from "./new-wallet-modal"
import { useContext } from "react"
import { BalanceContext } from "./wallets"
import { WalletTypeEnum } from "../[walletId]/history/_services/interfaces"
import { Button } from "@/common/components/ui/button"



export default function WalletFilter() {
    const { currencyType, setCurrencyType } = useContext(BalanceContext)


    return (
        <>
            <section className="flex items-center justify-between gap-4">
                <div className='flex items-center gap-4 w-full flex-wrap'>
                    <Select value={currencyType} onValueChange={setCurrencyType}>
                        <SelectTrigger className="w-[180px] h-[56px] rounded-full px-4">
                            <SelectValue
                                className="capitalize"
                                defaultValue={WalletTypeEnum.FIAT}
                            />
                        </SelectTrigger>
                        <SelectContent className="rounded-xl">
                            <SelectGroup className="rounded-full">
                                <SelectLabel>Currency type</SelectLabel>
                                {Object.entries(WalletTypeEnum).map(([key, value]) => (
                                    <SelectItem
                                        key={key} value={value}
                                        className="text-regular-16 text-gray-2 capitalize"
                                    >{value}</SelectItem>
                                ))}
                            </SelectGroup>
                        </SelectContent>
                    </Select>

                    <div className="relative flex items-center w-full max-w-[500px] px-4 border rounded-full">
                        <Input
                            placeholder="Search"
                            className="pr-9 h-[56px] rounded-full grow border-none outline-none"
                        />
                        <Button variant={"ghost"} className="flex-none w-max !hover:bg-none">
                            <Search className="h-4 w-4 text-gray-500" />
                        </Button>
                    </div>


                </div>
                <NewWalletModal />

            </section>
        </>
    )
}
