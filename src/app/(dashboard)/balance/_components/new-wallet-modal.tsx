import React, { useContext, useMemo, useState } from 'react'
import Clyp<PERSON>utton from '@/common/components/custom/clyp-button';
import { DialogContent, DialogTrigger } from '@/common/components/ui/dialog';
import { Dialog } from '@/common/components/ui/dialog';
import { LoaderIcon, Plus, Coins } from 'lucide-react';
import { Input } from '@/common/components/ui/input';
import { BalanceContext } from './wallets';
import { CryptoCurrencyEnum, FiatCurrencyEnum } from '../_services/interfaces';
import { Card, CardContent } from '@/common/components/ui/card';
import { cn } from '@/common/lib/utils';
import { useCreateWallet, useFetchCurrencies } from '../_services/queries/queries';
import { Button } from '@/common/components/ui/button';
import { toastErrorMessage, toastSuccessMessage } from '@/common/lib/toast-message';
import ClypOutlineButton from '@/common/components/custom/clyp-outline-button';
import { useQueryClient } from '@tanstack/react-query';
import { QUERY_KEYS } from '../_services/queries/query-keys';
import { useCookies } from '@/common/hooks/useCookies';
import EllipsisLoader from '@/common/components/custom/ellipsis-loader';

type SelectedWallet = string | null

export default function NewWalletModal() {
    const qc = useQueryClient()
    const [open, setOpen] = useState(false)
    const { currencyType,  } = useContext(BalanceContext)
    const [selectedWallet, setSelectedWallet] = useState<SelectedWallet>(null)
    const { getOrganizationUid } = useCookies();
    const organizationUid = getOrganizationUid();
    const {mutation} = useCreateWallet(organizationUid || '')

    const {data: currencies, isLoading: currenciesLoading} =  useFetchCurrencies(currencyType)


    const currentCurrencies = useMemo(() => {
        return currencies?.data ?? []
    }, [currencies])

    const handleCreateWallet = async () => {
        
        await mutation.mutateAsync({
            currency: selectedWallet as CryptoCurrencyEnum | FiatCurrencyEnum
        }).then((response) => {
            qc.invalidateQueries({queryKey: [QUERY_KEYS.WALLETS]})
            qc.refetchQueries({queryKey: [QUERY_KEYS.CURRENCIES]})
            const {data, message} = response
            toastSuccessMessage(message)
            if(data) {
                setSelectedWallet(null)
                setOpen(false)
            }
        }).catch((error) => {
            toastErrorMessage(error.message)
        })
    }
    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <ClypButton className="text-white bg-orange hover:bg-initial h-[50px]">
                    Add new
                    <Plus className="size-5" />
                </ClypButton>
            </DialogTrigger>
            <DialogContent 
                className="max-w-md px-6"
                onEscapeKeyDown={(e) => e.preventDefault()}
                onPointerDownOutside={(e) => e.preventDefault()}
            >
                <section className="space-y-7">
                    <div className="flex items-center gap-3 pt-5">
                        <ClypOutlineButton 
                            text="Back"
                            icon={'chevronLeft'}
                            className={cn(
                                "bg-orange-50 text-orange ring-0 h-[50px] w-[50px] rounded-full border-none outline-none")}
                            onClick={() => setOpen(false)}
                            hideText
                        />
                        <p className='text-bold-24 text-gray-2'>Add New Wallet</p>
                    </div>
                    <section className='flex flex-col items-center gap-7'>
                        <div className='flex items-center w-full border rounded-full h-[50px] px-3'>
                            <Input placeholder='Search' className='border-none outline-none grow pl-3' />
                        </div>
                        
                        {currenciesLoading ? (
                            <div className='flex justify-center w-full py-4'>
                                <EllipsisLoader />
                            </div>
                        ) : (
                            <div className="pt-5 w-full h-[250px] overflow-y-auto pr-1">
                                <div className="space-y-3">
                                    {currentCurrencies.map((option) => (
                                        <Card 
                                            key={option.currency} 
                                            className={cn(
                                                'group cursor-pointer',
                                                'shadow-none border-none hover:bg-gray-5 transition-all',
                                                selectedWallet === option.currency && 'bg-gray-5'
                                            )}
                                            onClick={() => setSelectedWallet(option.currency)}
                                        >
                                            <CardContent className='p-3'>
                                                <div className="w-full flex items-center">
                                                    <Coins className="mr-3 size-6 text-orange" />
                                                    <div className="flex flex-col">
                                                        <p className={cn(
                                                            'text-semibold-14 text-gray-4 group-hover:text-gray-white transition-all',
                                                            selectedWallet === option.currency && 'text-gray-white'
                                                        )}>
                                                            {option.currency}
                                                        </p>
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            </div>
                        )}

                        <Button 
                            className='w-full bg-orange hover:bg-initial text-white h-[50px]'
                            disabled={!selectedWallet || mutation.isLoading}
                            onClick={handleCreateWallet}
                        >
                            {mutation.isLoading ? <LoaderIcon className='size-7 animate-spin' /> : "Add Wallet"}
                        </Button>
                    </section>
                </section>
            </DialogContent>
        </Dialog>
    )
}
