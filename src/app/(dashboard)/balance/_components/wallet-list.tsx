"use client"
import { useContext, useMemo } from 'react'
import { BalanceContext } from './wallets'
import { useSearchParams } from 'next/navigation'
import { WalletTabEnums } from '../_services/enums'
import WalletCard from './wallet-card'

export default function WalletList() {
    const searchParams = useSearchParams()
    const tab = searchParams.get('type') || 'balances'

    const { wallets, currencyType } = useContext(BalanceContext)

    const currentWallets = useMemo(() => {
        return wallets.filter((wallet) => wallet.type === currencyType)
    }, [wallets, currencyType])


    console.log("currentWallets", currentWallets)

    if(currentWallets.length === 0) return (
        <div className='flex flex-col gap-4 text-center'>
            <p className='text-medium-16 text-gray-4'>No wallets found</p>
        </div>
    )
    return (
        <div className='grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6'>
            {currentWallets.map((wallet, index) => (
                <WalletCard
                    key={index}
                    currencyType={currencyType}
                    wallet={wallet}
                    tab={tab as WalletTabEnums}
                />
            ))}
        </div>
    )
}
