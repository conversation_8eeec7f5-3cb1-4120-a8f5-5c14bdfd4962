"use client"
import { createContext, useState } from 'react'
import WalletFilter from './wallet-filter'
import WalletList from './wallet-list'
import { WalletTypeEnum } from '../[walletId]/history/_services/interfaces'
import { useFetchWallets } from '../_services/queries/queries'
import { TWallet } from '../_services/interfaces'
import { useMyOrganization } from '@/app/create-organization/_services/queries/queries'
import EllipsisLoader from '@/common/components/custom/ellipsis-loader'

export type BalanceContextType = {
  currencyType: WalletTypeEnum
  setCurrencyType: (currencyType: WalletTypeEnum) => void
  wallets: TWallet[],
}


export const BalanceContext = createContext<BalanceContextType>({
  currencyType: WalletTypeEnum.FIAT,
  setCurrencyType: () => {},
  wallets: [],
})

export default function Wallets() {
  const [currencyType, setCurrencyType] = useState<WalletTypeEnum>(WalletTypeEnum.FIAT)

  const {data: orgData, isLoading: orgLoading} = useMyOrganization();

  const organization = orgData?.data;
  
  const {data: walletData, isLoading} = useFetchWallets(organization?.uid ?? '', !!organization);


  if(orgLoading || isLoading) {
    return <div className='flex justify-center items-center py-20'>
      <EllipsisLoader />
    </div>
  }



  return (  
    <BalanceContext.Provider value={{ 
      currencyType,
      setCurrencyType, 
      wallets: walletData?.data ?? []
    }}>
      <div className="space-y-8 py-6">
        <WalletFilter />
        <WalletList />
      </div>
    </BalanceContext.Provider>
  )
}
