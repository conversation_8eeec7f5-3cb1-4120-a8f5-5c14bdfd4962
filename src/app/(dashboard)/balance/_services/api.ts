import { apiRequest } from "@/common/lib/api-request";
import { TResponse } from "@/common/services/interfaces";
import { TWallet, TCurrency, TCreateBasicWallet } from "./interfaces";
import { TWalletHistory, WalletTypeEnum } from "../[walletId]/history/_services/interfaces";
import { TCreateFiatConversion, TSwapPrice } from "../swap/_services/interfaces";
import { getRoutes } from "@/common/services/constants";

export const GetWallets = async (ownerId: string) =>
    await apiRequest<undefined, TResponse<TWallet[]>>({
      path: getRoutes.getWallets(ownerId).path,
      method: getRoutes.getWallets(ownerId).method,
    });

export const GetCurrenciesByWalletType = async (type: WalletTypeEnum) =>
    await apiRequest<undefined, TResponse<TCurrency[]>>({
        path: getRoutes.getCurrenciesByWalletType(type).path,
        method: getRoutes.getCurrenciesByWalletType(type).method,
    });

export const GetWallet = async (walletId: string) =>
    await apiRequest<undefined, TResponse<TWallet>>({
        path: getRoutes.getWallet(walletId).path,
        method: getRoutes.getWallet(walletId).method,
    });

export const CreateWallet = async ({currency}: TCreateBasicWallet, ownerId: string) =>
    await apiRequest<TCreateBasicWallet, TResponse<TWallet>>({
        path: getRoutes.createWallet(ownerId).path,
        method: getRoutes.createWallet(ownerId).method,
        payload: { currency },
    });

export const GetConversionRate = async (walletId: string, data: TCreateFiatConversion) =>
    await apiRequest<TCreateFiatConversion, TResponse<TSwapPrice>>({
        path: `${getRoutes.getConversionRate().path}?amount=${data.amount}&from_currency=${data.from_currency}&to_currency=${data.to_currency}&method=${data.method}`,
        method: getRoutes.getConversionRate().method,
    });

export const GetWalletHistory = async (walletId: string) =>
    await apiRequest<undefined, TResponse<TWalletHistory[]>>({
        path: getRoutes.getTransactionHistory(walletId).path,
        method: getRoutes.getTransactionHistory(walletId).method,
    });

export const GetCurrencies = async (currencyType: WalletTypeEnum) =>
    await apiRequest<undefined, TResponse<TCurrency[]>>({
        path: getRoutes.getCurrencies(currencyType).path,
        method: getRoutes.getCurrencies(currencyType).method,
    });
