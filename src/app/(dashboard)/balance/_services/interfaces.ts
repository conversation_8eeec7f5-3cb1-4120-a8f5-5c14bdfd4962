import { BasicTableSchema } from "@/common/services/interfaces";
import { WalletMeta, WalletStatusEnum, WalletTypeEnum } from "../[walletId]/history/_services/interfaces";
import { z } from "zod";
import { CreateBasicWalletSchema } from "./validations";



export enum CryptoCurrencyEnum {
    BITCOIN = 'BTC',
    ETHEREUM = 'ETH',
    USDT = 'USDT',
    ATOM = 'ATOM',
}

export enum FiatCurrencyEnum {
    USD = 'USD',
    EUR = 'EUR',
    GBP = 'GBP',
    NGN = 'NGN',
    GHS = 'GHS',
}

export interface TCurrency extends BasicTableSchema {
    currency: string;
    precision: number;
    type: WalletTypeEnum
}

export interface PaymentWallet {
    uid: string;
    wallet_name: string;
    wallet_type: WalletTypeEnum;
    available_balance: string;
    currency: string;
}

export interface TWallet extends BasicTableSchema {
    account_uid: string;
    organization_uid: string;
    status: WalletStatusEnum;
    name: string;
    type: WalletTypeEnum;
    currency: string;
    balance: string;
    meta: WalletMeta;
    version: number;
}

// export interface TCreateWallet {
//     currency: CryptoCurrencyEnum | FiatCurrencyEnum;
//     walletType: WalletTypeEnum;
// }

export type TCreateBasicWallet = z.infer<typeof CreateBasicWalletSchema>

export interface TCurrency extends BasicTableSchema {
    currency: string;
    precision: number;
    type: WalletTypeEnum
}

export enum WalletOwnerEnum {
    USER = 'user',
    ORGANIZATION = 'organization',
  }