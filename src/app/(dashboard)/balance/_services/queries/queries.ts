"use client"
import { useMutation, UseMutationResult, useQuery } from "@tanstack/react-query"
import {
    CreateWallet,
    GetConversionRate,
    GetCurrencies,
    GetCurrenciesByWalletType,
    GetWalletHistory,
    GetWallets
} from "../api"
import { AxiosError } from "axios"
import { TCreateBasicWallet, TWallet } from "../interfaces"
import { TResponse } from "@/common/services/interfaces"
import { QUERY_KEYS } from "./query-keys"
import { WalletTypeEnum } from "../../[walletId]/history/_services/interfaces"
import { TCreateFiatConversion } from "../../swap/_services/interfaces"

export const useFetchWallets = (organizationId: string, enabler?: boolean) => {
    return useQuery({
        queryKey: [QUERY_KEYS.WALLETS],
        queryFn: () => GetWallets(organizationId),
        enabled: enabler,
    })
}

export const useCreateWallet = (ownerId: string) => {
    const mutation: UseMutationResult<
        TResponse<TWallet>,
        AxiosError,
        TCreateBasicWallet
    > = useMutation({
        mutationKey: ["create-wallet"],
        mutationFn: (data) => CreateWallet(data, ownerId),
    });

    return { mutation }
}

export const useFetchWalletHistory = (walletId: string) => {
    return useQuery({
        queryKey: [QUERY_KEYS.WALLET_HISTORY, walletId],
        queryFn: () => GetWalletHistory(walletId),
        enabled: !!walletId,
    })
}

export const useFetchCurrencies = (currencyType: WalletTypeEnum, enabler?: boolean) => {
    return useQuery({
        queryKey: [QUERY_KEYS.CURRENCIES, currencyType],
        queryFn: () => GetCurrencies(currencyType),
        enabled: enabler,
    })
}

export const useGetConversionRate = (walletId: string, data: TCreateFiatConversion, enabler?: boolean) => {
    return useQuery({
        queryKey: [QUERY_KEYS.CONVERSION_RATE],
        queryFn: () => GetConversionRate(walletId, data),
        enabled: enabler,
    })
}

export const useGetCurrenciesByWalletType = (type: WalletTypeEnum, enabler?: boolean) => {
    return useQuery({
        queryKey: [QUERY_KEYS.CURRENCIES],
        queryFn: () => GetCurrenciesByWalletType(type),
        enabled: enabler,
    })
}
