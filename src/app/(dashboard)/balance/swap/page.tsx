"use client"
import { SwapProvider } from './_hooks/swap-context'
import SwapPage from './swap-page'
import { TWallet } from '../_services/interfaces'
import { Suspense } from 'react'
import LoadingWallet from './_components/loading-wallet'

export interface TSwapFormProps {
    loading: boolean,
    wallet: TWallet | null,
    wallets: TWallet[]
}

export default function WalletPage() {
  return (
    <Suspense fallback={<LoadingWallet />}>
      <SwapProvider>
        <SwapPage />
      </SwapProvider>
    </Suspense>
  )
}
