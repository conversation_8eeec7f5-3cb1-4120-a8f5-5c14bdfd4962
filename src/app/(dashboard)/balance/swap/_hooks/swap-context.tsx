"use client"
import React, { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { TSwapPrice, SwapMethodEnums, TConvertCurrency } from '../_services/interfaces';
import { useSearchParams } from 'next/navigation';
import { TWallet } from '../../_services/interfaces';
import useGetWallet from './use-get-wallet';
import { ConvertCurrencySchema } from '../_services/validations';
import { useForm, UseFormReturn } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { WalletTypeEnum } from '../../[walletId]/history/_services/interfaces';
import { toastErrorMessage } from '@/common/lib/toast-message';
import { getConversionRate } from '../_services/utilities';

// Define the state type
type ConversionStateType = {
    toCurrency: TWallet | null;
    swapPrice: TSwapPrice | null;
};

// Define context type
interface SwapContextType {
    converting: boolean;
    conversionAmount: number | null;
    wallet: TWallet | null;
    form: UseFormReturn<TConvertCurrency>;
    formValues: TConvertCurrency;
    wallets: TWallet[];
    fetchingWallet: boolean;
    method: SwapMethodEnums;
    conversionState: ConversionStateType;
    updateConversionRate: () => Promise<void>;
    setConversionState: React.Dispatch<React.SetStateAction<ConversionStateType>>;
    setFromCurrency: (currency: string) => void;
    setConversionMethod: (currency: TWallet) => void;
    setToCurrency: (currency: TWallet) => void;
    setAmount: (amount: number) => void;
    setMethod: React.Dispatch<React.SetStateAction<SwapMethodEnums>>;
}

// Create context with a default value
const SwapContext = createContext<SwapContextType | undefined>(undefined);

const DEFAULT_AMOUNT = 1000;
const PRECISION = 2;
// Create a provider component
export function SwapProvider({ children }: { children: React.ReactNode }) {
    const { wallet, wallets, fetchingWallet } = useGetWallet();
    const searchParams = useSearchParams();
    
    const [converting, setConverting] = useState(false);
    const [conversionState, setConversionState] = useState<ConversionStateType>({
        toCurrency: null,
        swapPrice: null
    });
    
    const [method, setMethod] = useState<SwapMethodEnums>(SwapMethodEnums.FIAT_TO_FIAT);

    const form = useForm<TConvertCurrency>({
        resolver: zodResolver(ConvertCurrencySchema),
        defaultValues: {
            rate_id: "",
            from_currency: wallet?.currency || '',
            to_currency: searchParams?.get('to_currency') ?? '',
            amount: searchParams?.get('amount') ? Number(searchParams.get('amount')) : DEFAULT_AMOUNT,
        }
    });

    const amount = form.watch('amount');

    const numericAmount = typeof amount === 'string' 
        ? (isNaN(parseFloat(amount)) ? 0 : parseFloat(amount)) 
        : (isNaN(amount) ? 0 : amount);

    const formValues = form.watch();

    // Calculate conversion amount
    const conversionAmount = useMemo(() => {
        const toCurrency = conversionState.toCurrency;
        if (!conversionState.swapPrice?.rate || !numericAmount || !toCurrency) return null;

        const rate = conversionState.swapPrice.rate;
        return parseFloat((rate * numericAmount).toFixed(PRECISION));

        // eslint-disable-next-line
    }, [conversionState.swapPrice, numericAmount]);

    const setFromCurrency = (currency: string) => {
        form.setValue('from_currency', currency);
        // Explicitly trigger validation after setting the value
        form.trigger('from_currency');
    }

    const setConversionMethod = (currency: TWallet) => {
        setMethod(() => {
            if (!wallet) return SwapMethodEnums.FIAT_TO_FIAT;
            
            if(wallet.type === WalletTypeEnum.FIAT && currency.type === WalletTypeEnum.FIAT) {
                return SwapMethodEnums.FIAT_TO_FIAT;
            }
            else if(wallet.type === WalletTypeEnum.CRYPTO && currency.type === WalletTypeEnum.FIAT) {
                return SwapMethodEnums.CRYPTO_TO_FIAT;
            }
            else if(wallet.type === WalletTypeEnum.FIAT && currency.type === WalletTypeEnum.CRYPTO) {
                return SwapMethodEnums.FIAT_TO_CRYPTO;
            }
            else {
                return SwapMethodEnums.CRYPTO_TO_CRYPTO;
            }
        });
    }

    const setToCurrency = (currency: TWallet) => {
        form.setValue('to_currency', currency.currency);
        setConversionMethod(currency);
        
        // Explicitly trigger validation after setting the value
        form.trigger('to_currency');
        
        // Set the currency in the conversionState
        setConversionState(prev => ({
            ...prev,
            toCurrency: currency
        }));

        // Only trigger conversion rate update if we have all required values
        // and the from and to currencies are different
        if (wallet?.uid && 
            numericAmount > 0 && 
            wallet.currency && 
            currency.currency && 
            wallet.currency !== currency.currency) {
                
            // Don't need to call updateConversionRate as it would cause another render
            // Instead, update conversion rate directly
            setConverting(true);
            
            getConversionRate(
                wallet.uid,
                numericAmount,
                wallet.currency,
                currency.currency,
                method
            )
            .then(swapInfo => {
                console.log('swapInfo', swapInfo);
                if (swapInfo) {
                    setConversionState(prev => ({
                        ...prev,
                        swapPrice: swapInfo
                    }));
                }
            })
            .catch(error => {
                const errorMessage = error instanceof Error ? error.message : 'Error fetching conversion rate';
                toastErrorMessage(errorMessage);
            })
            .finally(() => {
                setConverting(false);
            });
        }
    }

    const setAmount = (amount: number) => {

        form.setValue('amount', amount);
        // Explicitly trigger validation after setting the value
        form.trigger('amount');

  
        const toCurrency = formValues.to_currency;
        if (wallet?.uid && 
            amount > 0 && 
            wallet.currency && 
            toCurrency && 
            wallet.currency !== toCurrency) {
                
            // Update conversion rate directly
            setConverting(true);
            
            getConversionRate(
                wallet.uid,
                amount,
                wallet.currency,
                toCurrency,
                method
            )
            .then(swapInfo => {
                if (swapInfo) {
                    setConversionState(prev => ({
                        ...prev,
                        swapPrice: swapInfo
                    }));
                }
            })
            .catch(error => {
                
                const errorMessage = error instanceof Error ? error.message : 'Error fetching conversion rate';
                toastErrorMessage(errorMessage);

            })
            .finally(() => {
                setConverting(false);
            });
        }
    }

    const updateConversionRate = useCallback(async () => {
        if (!wallet?.uid || !numericAmount || !formValues.to_currency) {
            return;
        }
        
        if (wallet.currency === formValues.to_currency) {
            return;
        }
        
        try {
            setConverting(true);

            const swapInfo = await getConversionRate(
                wallet.uid, 
                numericAmount,
                wallet.currency || '',
                formValues.to_currency, 
                method
            );

            if (swapInfo) {
                // Update context state
                setConversionState(prev => ({
                    ...prev,
                    swapPrice: swapInfo
                }));
            }
        } catch (error: unknown) {
            console.log('swap rate error', error);
            const errorMessage = error instanceof Error ? error.message : 'Error fetching conversion rate & fee';
            toastErrorMessage(errorMessage);
        } finally {
            setConverting(false);
        }
    }, [wallet, numericAmount, formValues.to_currency, method]); // Removed state setter from dependencies


    // Initialize from_currency when wallet changes
    useEffect(() => {
        if (wallet?.currency) {
            // Call the function but don't include it in dependencies
            form.setValue('from_currency', wallet.currency);
            form.trigger('from_currency');
        }
    }, [wallet, form]); // Remove setFromCurrency from dependencies


    // Memoize the context value to prevent unnecessary re-renders
    const contextValue = useMemo(() => ({
        form,
        formValues,
        wallet,
        wallets,
        fetchingWallet,
        conversionState,
        conversionAmount,
        converting,
        method,
        setConversionState,
        setFromCurrency,
        setConversionMethod,
        setToCurrency,
        setAmount,
        setMethod,
        updateConversionRate,
    // eslint-disable-next-line
    }), [
        wallet, 
        wallets, 
        fetchingWallet, 
        conversionState, 
        conversionAmount,
        converting,
        method, 
        form, 
        formValues
    ]);

    return (
        <SwapContext.Provider value={contextValue}>
            {children}
        </SwapContext.Provider>
    );
}

// Create a custom hook for accessing the context
export function useSwapContext() {
  const context = useContext<SwapContextType | undefined>(SwapContext);
  if (context === undefined) {
    throw new Error('useSwapContext must be used within a SwapProvider');
  }
  return context;
}
