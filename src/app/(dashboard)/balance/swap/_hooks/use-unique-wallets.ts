import { useMemo } from "react";
import { TWallet } from "../../_services/interfaces";

export default function useUniqueWallets(wallets: TWallet[]) {
    const uniqueWallets = useMemo(() => {
        const uniqueCurrencies = new Set<string>();
        return wallets.filter(wallet => {
            if (uniqueCurrencies.has(wallet.currency)) {
                return false;
            }
            uniqueCurrencies.add(wallet.currency);
            return true;
        });
    }, [wallets]);

    return uniqueWallets;
}