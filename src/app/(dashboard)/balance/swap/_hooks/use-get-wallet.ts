"use client"
import { TWallet } from "../../_services/interfaces";
import { useEffect, useState } from "react";
import { useFetchWallets } from "../../_services/queries/queries";
import { useSearchParams } from "next/navigation";
import { useCookies } from "@/common/hooks/useCookies";

export default function useGetWallet() {
    const searchParams = useSearchParams();
    const walletId = searchParams.get('wallet');
    const [wallet, setWallet] = useState<TWallet | null>(null);

    const { getOrganizationUid } = useCookies();

    const {
        data: walletsData, 
        isFetching: fetchingWallet
    } = useFetchWallets(getOrganizationUid()!, !!getOrganizationUid());

    useEffect(() => {
        if (walletsData?.data && walletId) {
            const foundWallet = walletsData.data.find(wallet => wallet.uid === walletId);
            setWallet(foundWallet ?? null);
        }
    }, [walletsData, walletId]);

    return {
        wallet,
        wallets: walletsData?.data || [],
        fetchingWallet
    }
}