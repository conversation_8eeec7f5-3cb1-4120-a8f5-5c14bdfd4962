// "use client"
// import { useCallback, useEffect, useMemo, useState } from "react";
// import { TWallet } from "../../_services/interfaces";
// import { getConversionRate } from "../_services/utilities";
// import { toastErrorMessage } from "@/common/lib/toast-message";
// import { useSwapContext } from "./swap-context";
// import { SwapMethodEnums } from "../_services/interfaces";

// const precision = 2;

// export default function useConversionRate(
//     wallet: TWallet | null,
//     amount: string | number, 
//     toCurrency: string
// ) {
//     // Get context state and updates
//     const { 
//         conversionState: contextState, 
//         setConversionState: setContextState, 
//         method 
//     } = useSwapContext();
    
//     const [converting, setConverting] = useState(false);
    
//     // Convert amount to number if it's a string and ensure it's a valid number
//     const numericAmount = typeof amount === 'string' 
//         ? (isNaN(parseFloat(amount)) ? 0 : parseFloat(amount)) 
//         : (isNaN(amount) ? 0 : amount);

//     // Calculate conversion amount
//     const conversionAmount = useMemo(() => {
//         if (!contextState.swapPrice?.rate || !numericAmount || !toCurrency) return null;

//         const rate = contextState.swapPrice.rate;
//         return parseFloat((rate * numericAmount).toFixed(precision));
//     }, [contextState.swapPrice, numericAmount, toCurrency]);

//     // Function to update conversion rate
//     const updateConversionRate = useCallback(async () => {
//         console.log('wallet', wallet)
//         console.log('numericAmount', numericAmount)
//         console.log('toCurrency', toCurrency)
//         console.log('wallet.currency', wallet?.currency)
//         if (!wallet?.uid || !numericAmount || !toCurrency || wallet.currency === toCurrency) {
//             console.log('Missing required params for conversion rate update');
//             return;
//         }
        
//         try {
//             setConverting(true);

//             const swapInfo = await getConversionRate(
//                 wallet.uid, 
//                 numericAmount,
//                 wallet.currency || '',
//                 toCurrency, 
//                 method as SwapMethodEnums
//             );

//             console.log('Received swap info:', swapInfo);
//             if (swapInfo) {
//                 // Update context state
//                 setContextState({
//                     ...contextState,
//                     swapPrice: swapInfo
//                 });
//             }
//         } catch (error: unknown) {
//             console.error('Error fetching conversion rate:', error);
//             const errorMessage = error instanceof Error ? error.message : 'Error fetching conversion rate & fee';
//             toastErrorMessage(errorMessage);
//         } finally {
//             setConverting(false);
//         }
//     }, [wallet, numericAmount, toCurrency, method, contextState, setContextState]);



//     return {
//         conversionAmount, 
//         updateConversionRate, 
//         converting
//     };
// }