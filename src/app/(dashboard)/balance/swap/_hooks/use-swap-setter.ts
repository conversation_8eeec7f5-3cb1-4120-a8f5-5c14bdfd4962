"use client"
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { TConvertCurrency } from "../_services/interfaces";
import { CreateFiatConversionShema } from "../_services/validations";
import { SwapMethodEnums } from "../_services/interfaces";
import { useEffect } from "react";

export default function useSwapSetter(formValues: TConvertCurrency, method: SwapMethodEnums) {
    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();
    
    const amount = formValues.amount;
    const from_currency = formValues.from_currency;
    const to_currency = formValues.to_currency;

    useEffect(() => {
        // Skip updating if the form values are invalid
        if(!CreateFiatConversionShema.safeParse({
            amount, 
            from_currency, 
            to_currency, 
            method
        }).success) return;

        const search = new URLSearchParams(searchParams);

        if(amount) search.set('amount', (amount ?? 0).toString());
        if(from_currency) search.set('from_currency', from_currency);
        if(to_currency) search.set('to_currency', to_currency);
        if(method) search.set('method', method);

        router.replace(`${pathname}?${search.toString()}`);
    }, [searchParams, amount, from_currency, to_currency, method, pathname, router]);
}