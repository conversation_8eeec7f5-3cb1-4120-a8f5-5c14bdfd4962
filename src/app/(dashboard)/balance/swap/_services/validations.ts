import { z } from "zod";
import { SwapMethodEnums } from "./interfaces";


export const CreateFiatConversionShema = z.object({
    amount: z.coerce.number().min(1, { message: 'Amount is required' }),
    from_currency: z.string().min(1, { message: 'From is required' }),
    to_currency: z.string().min(1, { message: 'To is required' }),
    method: z.nativeEnum(SwapMethodEnums, { message: 'Method is required' }),
})

export const ConvertCurrencySchema = z.object({
    rate_id: z.string(),
    amount: z.coerce.number(),
    from_currency: z.string(),
    to_currency: z.string(),
    network: z.string().optional(),
});