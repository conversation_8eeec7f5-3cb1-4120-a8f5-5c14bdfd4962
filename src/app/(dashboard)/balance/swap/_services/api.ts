import { apiRequest } from "@/common/lib/api-request";
import { TConvertCurrency, TCreateFiatConversion, TLockConversion, TLockConversionResponse, TSwapPrice } from "./interfaces";
import { TResponse } from "@/common/services/interfaces";
import { getRoutes } from "@/common/services/constants";

export const GetConversionRate = async (walletId: string, data: TCreateFiatConversion) =>
    await apiRequest<TCreateFiatConversion, TResponse<TSwapPrice>>({
        path: `${getRoutes.getConversionRate().path}?amount=${data.amount}&from_currency=${data.from_currency}&to_currency=${data.to_currency}&method=${data.method}`,
        method: getRoutes.getConversionRate().method,
    });

export const CreateFiatConversion = async (walletId: string, data: TConvertCurrency) =>
    // eslint-disable-next-line
    await apiRequest<TConvertCurrency, TResponse<any>>({
        path: getRoutes.convertCurrency(walletId).path,
        method: getRoutes.convertCurrency(walletId).method,
        payload: data,
    }); 

export const lockConversion = async (data: TLockConversion) =>
    await apiRequest<TLockConversion, TResponse<TLockConversionResponse>>({
        path: getRoutes.lockConversion().path,
        method: getRoutes.lockConversion().method,
        payload: data,
    });