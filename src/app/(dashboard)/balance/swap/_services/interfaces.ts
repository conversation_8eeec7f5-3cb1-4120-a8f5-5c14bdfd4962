import { z } from "zod";
import { CreateFiatConversionShema, ConvertCurrencySchema } from "./validations";


export enum SwapMethodEnums {
    CRYPTO_TO_CRYPTO = 'CRYPTO_TO_CRYPTO',
    CRYPTO_TO_FIAT = 'CRYPTO_TO_FIAT',
    FIAT_TO_CRYPTO = 'FIAT_TO_CRYPTO',
    FIAT_TO_FIAT = 'FIAT_TO_FIAT',
}

export type TCreateFiatConversion = z.infer<typeof CreateFiatConversionShema>

export type TGetConversionFee = {
    amount: number;
    method: SwapMethodEnums;

}

export interface TSwapPrice {
    rate: number;
    rate_id: string;
    fee?: number;
}

export interface TTransactionFee {
    fee: number;
}

export interface TLockConversion {
    price_id: string;
}
  
export interface TLockConversionResponse {
    locked_rate_id: string;
}

export type TConvertCurrency = z.infer<typeof ConvertCurrencySchema>

export interface TTransactionFee {
    fee: number;
  }
  
export interface TSwapFee {
    name: string;
    buy_fee: number;
    sell_fee: number;
}
