import { GetConversionRate } from "./api";
import { TCreateFiatConversion } from "./interfaces";
import { SwapMethodEnums } from "./interfaces";

export const getConversionRate = async(
    walletId: string, 
    amount: number,
    fromCurrency: string, 
    toCurrency: string, 
    method: SwapMethodEnums
) => {

    try {
        const conversionParams: TCreateFiatConversion = {
            amount,
            from_currency: fromCurrency,
            to_currency: toCurrency,
            method
        };
        
        const response = await GetConversionRate(walletId, conversionParams);
        
        const swapPriceData = response.data;
        
        return swapPriceData ?? null;
    } catch (error) {
        throw error;
    }
}