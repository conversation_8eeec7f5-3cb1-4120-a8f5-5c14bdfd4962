import { useMutation, UseMutationResult, useQuery } from "@tanstack/react-query";
import { SwapQueryKeys } from "./query-keys";
import { CreateFiatConversion, GetConversionRate, lockConversion } from "../api";
import { TConvertCurrency, TCreateFiatConversion, TLockConversionResponse, TLockConversion } from "../interfaces";
import { CreateFiatConversionShema } from "../validations";
import { AxiosError } from "axios";
import { TResponse } from "@/common/services/interfaces";

export const useGetConversionRate = (walletId: string, data: TCreateFiatConversion, enabler?: boolean) => {
    return useQuery({
        queryKey: [SwapQueryKeys.conversionRate],
        queryFn: () => GetConversionRate(walletId, data),
        enabled: enabler ?? CreateFiatConversionShema.safeParse(data).success
    })
}

export const useCreateFiatConversion = (walletId: string) => {
    const mutation: UseMutationResult<
        // eslint-disable-next-line
        TResponse<any>,
        AxiosError,
        TConvertCurrency
    > = useMutation({
        mutationKey: ["create-fiat-conversion"],
        mutationFn: (data: TConvertCurrency) => CreateFiatConversion(walletId, data),
    });

    return { mutation }
}

export const useLockConversion = () => {
    const mutation: UseMutationResult<
        TResponse<TLockConversionResponse>,
        AxiosError,
        TLockConversion
    > = useMutation({
        mutationKey: ["lock-conversion"],
        mutationFn: lockConversion,
    });

    return { mutation }
}