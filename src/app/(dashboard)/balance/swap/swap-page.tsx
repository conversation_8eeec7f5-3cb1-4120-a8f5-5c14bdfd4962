"use client"
import CryptoSwapForm from './_components/crypto-swap-form'
import FiatSwapForm from './_components/fiat-swap-form'
import BackButton from '@/common/components/custom/back-button'
import { Card } from '@/common/components/ui/card'
import { useSwapContext } from './_hooks/swap-context'
import { WalletTypeEnum } from '../[walletId]/history/_services/interfaces'
import LoadingWallet from './_components/loading-wallet'
import WalletNotFound from './_components/wallet-not-found'


export default function SwapPage() {
    const { wallet, wallets, fetchingWallet } = useSwapContext();

    if (fetchingWallet) return <LoadingWallet />

    
    if (!wallet) return <WalletNotFound />

    return (
    <div className="container">
          <div className='space-y-10 max-w-[900px]'>
              <BackButton />

              <Card className='space-y-7 border-none shadow-none max-w-[450px]'>
                  <h1 className="text-bold-24 text-gray-2">Swap {wallet.type}</h1>

                  <div className='space-y-4'>
                      <p className="text-bold-20 text-orange">Convert currency</p>
                      <p className="text-gray-2 text-light-18">
                      Swap between your fiat balance
                      </p>
                  </div>
              </Card>
            
              {wallet.type === WalletTypeEnum.FIAT ? (
                  <FiatSwapForm
                      loading={fetchingWallet} 
                      wallet={wallet}
                      wallets={wallets}
                  />
              ) : (
                  <CryptoSwapForm
                      loading={fetchingWallet}
                      wallet={wallet}
                      wallets={wallets}
                  />
              )}
          </div>
      </div>
  )
}
