import { Card, CardContent } from '@/common/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/common/components/ui/select'
import React from 'react'
import { TSwapFormProps } from '../page'
import EllipsisLoader from '@/common/components/custom/ellipsis-loader'
import ClypButton from '@/common/components/custom/clyp-button'

export default function CryptoSwapForm({ wallets, loading }: TSwapFormProps) {
  return (
    <section className='space-y-7'>
        <div className='grid grid-cols-2 gap-5'>
            <Card className="shadow-none">
                <CardContent className="p-3">
                    <p className='text-regular-18'>From</p>
                    <div className='flex items-center gap-2 justify-between'>
                        <Select>
                            <SelectTrigger className='w-[100px] border border-orange text-orange bg-white'>
                                <SelectValue placeholder='Coin' />
                            </SelectTrigger>
                            {
                                loading ? (
                                    <EllipsisLoader />
                                ) : (
                                    <SelectContent>
                                        {wallets.map((wallet) => (
                                            <SelectItem key={wallet.uid} value={wallet.currency}>
                                                {wallet.currency}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                )}
                        </Select>

                        <div>
                            <p className='text-medium-14'>Available balance</p>
                            <p className='text-semibold-20'>0.001 - 34,000</p>
                        </div>
                    </div>
                </CardContent>
            </Card>
            <Card className="shadow-none">
                <CardContent className="p-3">
                    <p className='text-regular-18'>To</p>
                    <div className='flex items-center gap-2 justify-between'>
                        <Select>
                            <SelectTrigger className='w-[100px] border border-orange text-orange bg-white'>
                                <SelectValue placeholder='Coin' />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value='usd'>USD</SelectItem>
                                <SelectItem value='eur'>EUR</SelectItem>
                                <SelectItem value='gbp'>GBP</SelectItem>
                            </SelectContent>
                        </Select>

                        <div>
                            <p className='text-medium-14 opacity-0'>Available balance</p>
                            <p className='text-semibold-20'>0.001 - 34,000</p>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
        {/* <ConversionPreview /> */}
        <ClypButton 
            classes="w-full shadow-none h-[50px] min-w-[200px] outline-none text-white"
            text="Swap Crypto"
        />
    </section>
  )
}
