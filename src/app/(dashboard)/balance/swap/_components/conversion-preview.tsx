"use client"
import { useCallback, useState } from 'react'
import Clyp<PERSON>utton from '@/common/components/custom/clyp-button';
import { DialogContent, DialogTrigger } from '@/common/components/ui/dialog';
import { Dialog } from '@/common/components/ui/dialog';
import BackButton from '@/common/components/custom/back-button';
import { Table, TableBody, TableCell, TableRow } from '@/common/components/ui/table';
import EllipsisLoader from '@/common/components/custom/ellipsis-loader';
import { useCreateFiatConversion, useLockConversion } from '../_services/queries/queries';
import { TLockConversionResponse } from '../_services/interfaces';
import { toastErrorMessage, toastSuccessMessage } from '@/common/lib/toast-message';
import { useSwapContext } from '../_hooks/swap-context';
import { QUERY_KEYS as BALANCE_QUERY_KEYS } from "../../../balance/_services/queries/query-keys"
import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';


const precision = 2;

interface TConversionPreviewProps {
    walletId: string,
    disabled?: boolean, 
    isLoading?: boolean,
    amount: number,
    from_currency: string,
}
export default function ConversionPreview({ 
    walletId,
    disabled, 
    isLoading,
    amount, 
    from_currency 
}: TConversionPreviewProps) {
    const queryClient = useQueryClient();
    // Get the context state 
    const { conversionState: contextState } = useSwapContext();

    const router = useRouter();
    const [isOpen, setIsOpen] = useState(false);
    const [lock, setLock] = useState<TLockConversionResponse | null>(null);
    
    const {mutation: lockConversion} = useLockConversion();
    const {mutation: convertCurrency} = useCreateFiatConversion(walletId);

    const conversionPrecision = precision;
    const conversionAmount = ((contextState.swapPrice?.rate ?? 0) * amount).toFixed(conversionPrecision);
    const conversionCurrency = contextState.toCurrency?.currency ?? '';

    const handleLockConversion = useCallback(() => {
        if (contextState.swapPrice?.rate_id && !lockConversion.isLoading) {
            // Assuming lockConversion is a mutation from React Query
            lockConversion
                .mutateAsync({price_id: contextState.swapPrice.rate_id })
                .then((response) => {
                    setLock(response.data);
                })
                .catch((error) => {
                    console.error('Error locking conversion:', error);
                    toastErrorMessage(error?.message ?? 'Error locking conversion');
                });
        }
    }, [lockConversion, contextState]);

    const handleConvertCurrency = async() => {
        if(!lock?.locked_rate_id) {
            toastErrorMessage('Transaction lock error detected');
            return;
        };

        convertCurrency.mutateAsync({
            rate_id: lock?.locked_rate_id ?? '',
            amount: amount,
            from_currency: from_currency,
            to_currency: conversionCurrency,
        })
        .then(() => {
            toastSuccessMessage('Conversion initiated');
            queryClient.invalidateQueries({ queryKey: [BALANCE_QUERY_KEYS.WALLETS] });
            router.push(`/balance?type=swap`);
        })
        .catch((error) => {  
            toastErrorMessage(error?.message ?? 'Error converting currency');
        });
    }
    

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <ClypButton 
                    classes="bg-gray-2 shadow-none h-[50px] min-w-[200px] outline-none text-white"
                    text="Preview conversion"
                    type="button"
                    disabled={disabled}
                    isLoading={isLoading}
                    onClick={handleLockConversion}
                />
            </DialogTrigger>
            <DialogContent 
                className="max-w-[450px]"
                onEscapeKeyDown={(e) => e.preventDefault()}
                onPointerDownOutside={(e) => e.preventDefault()}
            >
                {lockConversion.isLoading ? (
                    <div className='h-[400px] flex justify-center items-center'>
                        <EllipsisLoader />
                    </div>
                ):(
                    <section className='pt-10 flex flex-col items-center gap-7'>
                        <div className="flex items-center gap-4 w-full px-3">
                            <BackButton action={() => setIsOpen(false)} hideText className='w-[48px] h-[48px]' />
                            <h3 className='text-semibold-24'>Conversion Preview </h3>
                        </div>
                        <div className="space-y-1 text-center w-full px-3">
                            {
                                isLoading ? (
                                    <div className='flex justify-center items-center h-full'>
                                        <EllipsisLoader />
                                    </div>
                                ) : (
                                    <Table>
                                        <TableBody>
                                            <TableRow className='border-none'>
                                                <TableCell className='py-3 text-left text-regular-16 text-gray-4'>From</TableCell>
                                                <TableCell className='py-3 text-right text-semibold-16 text-gray-2'>
                                                    {from_currency}
                                                </TableCell>
                                            </TableRow>
                                            <TableRow className='border-none'>
                                                <TableCell className='py-3 text-left text-regular-16 text-gray-4'>To</TableCell>
                                                <TableCell className='py-3 text-right text-semibold-16 text-gray-2'>
                                                    {conversionCurrency}
                                                </TableCell>
                                            </TableRow>
                                            <TableRow className='border-none'>
                                                <TableCell className='py-3 text-left text-regular-16 text-gray-4'>Amount (From)</TableCell>
                                                <TableCell className='py-3 text-right text-semibold-16 text-gray-2'>
                                                    {amount}
                                                </TableCell>
                                            </TableRow>
                                            <TableRow className='border-none'>
                                                <TableCell className='py-3 text-left text-regular-16 text-gray-4'>Amount (To)</TableCell>
                                                <TableCell className='py-3 text-right text-semibold-16 text-gray-2'>
                                                    {conversionAmount} {conversionCurrency}
                                                </TableCell>
                                            </TableRow>
                                            <TableRow className='border-none'>
                                                <TableCell className='py-3 text-left text-regular-16 text-gray-4'>Fee</TableCell>
                                                <TableCell className='py-3 text-right text-semibold-16 text-gray-2'>
                                                    {contextState.swapPrice?.fee} {conversionCurrency}
                                                </TableCell>
                                            </TableRow>
                                            <TableRow className='border-none'>
                                                <TableCell className='py-3 text-left text-regular-16 text-gray-4'>Date</TableCell>
                                                <TableCell className='py-3 text-right text-semibold-16 text-gray-2'>
                                                    {new Date().toLocaleDateString()} | {new Date().toLocaleTimeString()}
                                                </TableCell>
                                            </TableRow>
                                        </TableBody>
                                    </Table>
                                )
                            }
                        </div>
                        <div className="flex flex-col items-center gap-5">
                            <ClypButton 
                                classes="w-full shadow-none h-[50px] min-w-[200px] outline-none text-white"
                                text="Swap Crypto"   
                                disabled={isLoading || convertCurrency.isLoading}
                                onClick={handleConvertCurrency}
                            />
                        </div>
                    </section>
                )}
            </DialogContent>
        </Dialog>
    )
}
