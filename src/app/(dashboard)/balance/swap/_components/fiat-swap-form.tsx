"use client"
import { Card, CardContent } from '@/common/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/common/components/ui/select';
import EllipsisLoader from '@/common/components/custom/ellipsis-loader';
import { Input } from '@/common/components/ui/input';
import { TSwapFormProps } from '../page';
import { Form, FormControl, FormMessage, FormItem, FormField } from '@/common/components/ui/form';
import useSwapSetter from '../_hooks/use-swap-setter';
import ConversionPreview from './conversion-preview';
import useUniqueWallets from '../_hooks/use-unique-wallets';
import { Skeleton } from '@/common/components/ui/skeleton';
import { useSwapContext } from '../_hooks/swap-context';

export default function FiatSwapForm({ loading, wallet, wallets }: TSwapFormProps) {
    // Get context state
    const { 
        conversionState,
        formValues,
        form,
        method,
        setToCurrency,
        setConversionMethod,
        setAmount,
        conversionAmount,
        converting
    } = useSwapContext();
    
    // Call hooks unconditionally at the top level
    useSwapSetter(formValues, method);
    const uniqueWallets = useUniqueWallets(wallets || []);

    if (loading || !wallet) {
        return (
            <div className="flex justify-center items-center min-h-[300px]">
                <EllipsisLoader />
            </div>
        );
    }

    return (
        <Form {...form}>
        <form className='space-y-7'>
            <div className='space-y-4'>
                <FormField
                    control={form.control}
                    name="amount"
                    render={({ field }) => (
                        <FormItem>
                            <FormControl>
                            <Input
                                {...field}
                                placeholder='Enter amount'
                                type='number'
                                className='w-[200px]'
                                onChange={(e) => {
                                    const value = Number(e.target.value);
                                    setAmount(value);
                                }}
                            />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

            <div className='grid grid-cols-2 gap-5'>
            <Card className="shadow-none">
                <CardContent className="p-3">
                    <p className='text-regular-18'>From</p>
                    <div className='flex items-center gap-2 justify-between'>
                        <div className='w-[100px] border border-orange text-orange bg-white h-[38px] rounded-md flex items-center justify-center'>
                            {formValues.from_currency}
                        </div>
                        <div>
                            <p className='text-medium-14'>Available balance</p>
                            <p className='text-bold-20'>
                                {`${wallet.currency} ${Number(wallet.balance).toLocaleString()}`}
                            </p>
                        </div>
                </div>
                </CardContent>
            </Card>

            <Card className="shadow-none">
                <CardContent className="p-3">
                    <div className='flex items-center gap-2 justify-between'>
                        <div className="flex flex-col gap-2">
                            <p className='text-regular-18'>To</p>
                            {
                                loading ? (
                                    <div className="pt-4">
                                        <EllipsisLoader />
                                </div>
                                ) : (
                                    <Select 
                                        onValueChange={(value) => {
                                            const currencyWallet = wallets.find(wallet => wallet.currency === value);
                                            
                                            if (currencyWallet) {
                                                
                                                // First set context wallet and method
                                                setToCurrency(currencyWallet);
                                                setConversionMethod(currencyWallet);
                                                
                                                // Then update form value
                                                form.setValue('to_currency', value);
                                                form.trigger('to_currency');
                                            }
                                        }} 
                                        value={formValues.to_currency}
                                    >
                                        <SelectTrigger 
                                            className='w-[100px] border border-orange text-orange bg-white'>
                                            <SelectValue placeholder='Account' />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {uniqueWallets.map((walletItem) => (
                                                <SelectItem 
                                                    key={walletItem.uid} 
                                                    value={walletItem.currency}
                                                    disabled={walletItem.currency === formValues.from_currency}
                                                >
                                                    {walletItem.currency}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                )
                            }
                        </div>

                        {!loading && (
                            <div className='flex flex-col gap-2 min-w-[100px]'>
                                <p className='text-medium-14'>Amount</p>
                                {converting? (
                                    <Skeleton className='w-[100px] h-9' />
                                ) : (
                                    <p className='text-bold-20'>
                                        {conversionAmount ?? "?"}
                                    </p>
                                )}
                            </div>
                        )}
                    </div>
                </CardContent>
            </Card>
            </div>
            <p>
                {
                converting ? (
                     <span className='text-regular-14 text-neutral-600 flex items-center gap-2'>
                        Calculating fee... <EllipsisLoader size={8} />
                    </span>
                ) : !conversionState.swapPrice?.rate ? (
                    <span className='text-sm text-neutral-600'>Please select currency to convert to</span>
                ) : (
                    `${(conversionState.swapPrice?.fee ?? 0).toFixed(2)} Fee`
                )
            }
            </p>
            </div>
                <ConversionPreview 
                    walletId={wallet.uid}
                    disabled={
                        converting || 
                        !conversionState.swapPrice || 
                        !(formValues.amount && formValues.from_currency && formValues.to_currency)
                    } 
                    isLoading={converting}
                    amount={formValues.amount}
                    from_currency={formValues.from_currency}
                />
            </form>
        </Form>
    );
}