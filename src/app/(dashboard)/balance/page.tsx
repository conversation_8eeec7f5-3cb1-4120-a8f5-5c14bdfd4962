"use client"
import Wallets from './_components/wallets'
import CustomTabs from '@/common/components/custom/custom-tabs'
import { WalletTabEnums } from './_services/enums'
import { Suspense } from 'react'
import LoadingWallet from './swap/_components/loading-wallet'

export default function BalancePage() {
  return (
    <Suspense fallback={<LoadingWallet />}>
      <div className='px-6'>
        <CustomTabs
          defaultValue={WalletTabEnums.BALANCES}
          property='type'
          tabs={[
            {
              label: 'Balances',
              value: WalletTabEnums.BALANCES,
              content: <Wallets />
            },
            {
              label: 'Swap',
              value: WalletTabEnums.SWAP,
              content: <Wallets />
            }
          ]}
        />
      </div>
    </Suspense>
  )
}
