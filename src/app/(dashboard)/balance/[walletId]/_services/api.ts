import { TCreateDeposit, TCreateDepositResponse } from "./interfaces";
import { apiRequest } from "@/common/lib/api-request";
import { TResponse } from "@/common/services/interfaces";
import { getRoutes } from "@/common/services/constants";

export const CreateDeposit = async (data: TCreateDeposit) =>
    await apiRequest<TCreateDeposit, TResponse<TCreateDepositResponse>>({
        path: getRoutes.deposit().path,
        method: getRoutes.deposit().method,
        payload: data,
    });

