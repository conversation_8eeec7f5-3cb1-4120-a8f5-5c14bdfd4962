import { UseMutationResult } from "@tanstack/react-query";

import { TResponse } from "@/common/services/interfaces";
import { useMutation } from "@tanstack/react-query";
import { TCreateDeposit, TCreateDepositResponse } from "../interfaces";
import { AxiosError } from "axios";
import { CreateDeposit } from "../api";


export const useCreateDeposit = () => {
    const mutation: UseMutationResult<
        TResponse<TCreateDepositResponse>,
        AxiosError,
        TCreateDeposit
    > = useMutation({
        mutationKey: ["create-deposit"],
        mutationFn: (data: TCreateDeposit) => CreateDeposit(data),
    });

    return { mutation }
}