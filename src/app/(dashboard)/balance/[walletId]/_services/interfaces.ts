import { z } from "zod";
import { CreateDepositSchema } from "./validations";

export type TCreateDeposit = z.infer<typeof CreateDepositSchema>


export enum CreateDepositStatusEnum {
    CREATED = 'created',
}

export interface CreateDepositLinkDetails {
    id: string;
    status: CreateDepositStatusEnum;
    use_link: boolean;
    link: string;
    payment_detail: string;
}

export interface TCreateDepositResponse {
    fee: number;
    status: CreateDepositLinkDetails['status'];
    link: CreateDepositLinkDetails['link'];
}