"use client"

import { useEffect } from 'react'
import { TWallet } from '../../_services/interfaces'
import { Form, FormField, FormItem, FormMessage, FormControl } from '@/common/components/ui/form'
import { TCreateDeposit } from '../_services/interfaces'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { CreateDepositSchema } from '../_services/validations'
import { useGetProfile } from '@/app/(auth)/_services/queries/queries'
import ClypButton from '@/common/components/custom/clyp-button'
import PlaceholderFloatInput from '@/common/components/custom/placeholder-float-input'
import { toastErrorMessage, toastSuccessMessage } from '@/common/lib/toast-message'
import { useCreateDeposit } from '../_services/queries/queries'
import { CheckoutPaymentChannel } from '@/common/services/interfaces'


export default function CryptoTransferForm({ wallet }: { wallet: TWallet }) {

    const { mutation } = useCreateDeposit()

    const { data: profile } = useGetProfile()

    const form = useForm<TCreateDeposit>({
        resolver: zodResolver(CreateDepositSchema),
        defaultValues: {
            amount: 0,
            currency: wallet.currency,
            email: '',
            channels: [CheckoutPaymentChannel.CRYPTO]
        },
    })

    useEffect(() => {
        if (profile) {
            form.setValue('email', profile.data.account.email)
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [profile])

    const onSubmit = (data: TCreateDeposit) => {
        mutation.mutateAsync(data)
            .then((response) => {

                toastSuccessMessage('Fund transfer initiated successfully')
                window.location.href = response.data.link
            })
            // eslint-disable-next-line
            .catch((error: any) => {
                toastErrorMessage(error.message || 'Failed to initiate fund transfer')
            })
    }

    return (
        <div className='space-y-4'>
            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)}>
                    <section className='grid grid-cols-2 gap-7'>
                        <div className="">
                            <FormField
                                control={form.control}
                                name="amount"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormControl>
                                            <PlaceholderFloatInput
                                                field={field}
                                                placeholder=''
                                                type='number'
                                                name='amount'
                                                onChange={e => field.onChange(e.target.value)}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                        {/* <div className="">
                            <FormField
                                control={form.control}
                                name="network"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormControl>
                                            <PlaceholderFloatInput
                                                field={field}
                                                placeholder='Network'
                                                name='network'
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                        <div className="flex items-center gap-2 border px-3 rounded-full">
                            <Input
                                className='h-[56px] p-2 outline-none border-none'
                                placeholder='Wallet Address'
                                disabled
                            />
                            <CopyIcon className='size-5 text-orange' role='button' />
                        </div> */}
                    </section>
                    <ClypButton
                        className='h-[50px] text-medium-18 text-white w-max mt-6 rounded-full'
                        text='Proceed to payment'
                        type="submit"
                        disabled={!form.formState.isValid || mutation.isLoading}
                        isLoading={mutation.isLoading}
                    />
                </form>
            </Form>

        </div>
  )
}
