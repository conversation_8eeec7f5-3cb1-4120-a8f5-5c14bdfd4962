"use client"

// import { But<PERSON> } from '@/common/components/ui/button'
import { Card } from '@/common/components/ui/card'
import { Input } from '@/common/components/ui/input'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { CreateDepositSchema } from '../_services/validations'
import { TCreateDeposit } from '../_services/interfaces'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/common/components/ui/form'
import { useCreateDeposit } from '../_services/queries/queries'
import { toastErrorMessage, toastSuccessMessage } from '@/common/lib/toast-message'
import FundInitiationDialog from './fund-initiation-details'
import { TWallet } from '../../_services/interfaces'
import { useGetProfile } from '@/app/(auth)/_services/queries/queries'
import EllipsisLoader from '@/common/components/custom/ellipsis-loader'
import { CheckoutPaymentChannel } from '@/common/services/interfaces'
import ClypButton from '@/common/components/custom/clyp-button'

export default function FiatTransferForm({wallet}: {wallet: TWallet}) {

    const { mutation } = useCreateDeposit()
    const [openModal, setOpenModal] = useState<boolean>(false)
    const { data: profile, isLoading: loadingProfile } = useGetProfile()


    const form = useForm<TCreateDeposit>({
        resolver: zodResolver(CreateDepositSchema),
        defaultValues: {
            amount: 100,
            email: '',
            currency: wallet.currency,
            channels: [CheckoutPaymentChannel.BANK_TRANSFER]
        }
    })

    useEffect(() => {
        if (profile) {
            form.setValue('email', profile.data.account.email)
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [profile])

    const onSubmit = (data: TCreateDeposit) => {
        mutation.mutateAsync(data)
            .then(() => {
                toastSuccessMessage('Fund transfer initiated successfully')
                setOpenModal(true)
            })
            // eslint-disable-next-line
            .catch((error: any) => {
                toastErrorMessage(error.message || 'Failed to initiate fund transfer')
            })
    }

    return (
        <div>
            {
                loadingProfile ? (
                    <div className='flex justify-center items-center h-full'>
                        <EllipsisLoader />
                    </div>
                ) : (
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)}>
                            <Card className='space-y-7 border-none shadow-none max-w-[450px]'>
                                <div className="space-y-1">
                                    <FormField
                                        control={form.control}
                                        name="amount"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormControl>
                                                    <Input
                                                        className='h-[56px] p-2 px-4 rounded-full'
                                                        placeholder='Enter Amount'
                                                        type="number"
                                                        min={100}
                                                        {...field}
                                                        onKeyDown={e => {
                                                            // Allow only numbers, backspace, delete, tab, arrows
                                                            const allowedKeys = ['Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight', '.'];
                                                            if (!allowedKeys.includes(e.key) && !/[0-9]/.test(e.key)) {
                                                                e.preventDefault();
                                                            }
                                                        }}
                                                        onChange={e => {
                                                            const value = e.target.value;
                                                            // Only update if it's a valid number
                                                            if (!isNaN(Number(value))) {
                                                                field.onChange(parseFloat(value));
                                                            } else {
                                                                // Reset to previous valid value
                                                                e.target.value = field.value?.toString() || '';
                                                            }
                                                        }}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>
                                <h1 className="text-semibold-20">
                                    NGN {
                                        form.watch('amount') ? (form.watch('amount').toLocaleString()) : "0.00"
                                    } will be deposited
                                </h1>

                                <ClypButton
                                    className='h-[50px] w-[60%] bg-gray-2 text-medium-18 text-white'
                                    type="submit"
                                    disabled={mutation.isLoading || !form.formState.isValid}
                                    text={`Generate payment details`}
                                    isLoading={mutation.isLoading}
                                />
                            </Card>
                        </form>
                    </Form>
                )
            }


            <FundInitiationDialog
                details={mutation.data?.data}
                isOpen={openModal}
                onOpenChange={setOpenModal}
                amount={form.getValues('amount')}
                currency={wallet.currency}
            />
        </div>
    )
}
