"use client"

import React from 'react'
import { TCreateDepositResponse } from '../_services/interfaces'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/common/components/ui/dialog'
import ClypButton from '@/common/components/custom/clyp-button'
import { Table, TableCell, TableHead, TableRow } from '@/common/components/ui/table'
import Link from 'next/link'

interface InitiationDetailsProp {
    details?: TCreateDepositResponse,
    amount: number,
    currency: string,
    isOpen: boolean,
    onOpenChange: (open: boolean) => void
}

export default function FundInitiationDialog({
    details, 
    amount, 
    currency,
    isOpen,
    onOpenChange
}: InitiationDetailsProp) {
    
    return (
        <Dialog open={isOpen} onOpenChange={onOpenChange}>
            {
                !details ? (
                    <DialogContent>
                        <DialogTitle>No response received</DialogTitle>
                    </DialogContent>
                ) : <>
                    <DialogContent 
                        className="max-w-sm px-6"
                        onEscapeKeyDown={(e) => e.preventDefault()}
                        onPointerDownOutside={(e) => e.preventDefault()}
                    >
                        <DialogHeader>
                            <DialogTitle>Pay {currency} {amount}</DialogTitle>
                        </DialogHeader>
                        <section className='pt-10 flex flex-col justify-center items-center gap-5'>
                            <Table className="border-collapse [&_tr]:border-0 [&_td]:border-0 [&_th]:border-0">
                                <TableRow>
                                    <TableHead className='text-medium-16'>Amount</TableHead>
                                    <TableCell className='text-regular-16'>{currency}{amount}</TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableHead className='text-medium-16'>Fee</TableHead>
                                    <TableCell className='text-regular-16'>{amount}</TableCell>
                                </TableRow>
                            </Table>

                            <Link href={details.link}>
                                <ClypButton
                                    text="Continue to payment"
                                    className='bg-orange w-full rounded-full h-[50px] text-white'
                                />
                            </Link>
                        </section>
                    </DialogContent>
                </>
            }
        </Dialog>
    )
}