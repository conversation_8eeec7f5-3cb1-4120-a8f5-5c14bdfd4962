import React from 'react'
import Cly<PERSON>Button from '@/common/components/custom/clyp-button';
import { Icons } from '@/common/components/custom/icon-templates';
import { Button } from '@/common/components/ui/button';
import { DialogContent } from '@/common/components/ui/dialog';
import { Dialog } from '@/common/components/ui/dialog';
import Link from 'next/link';


interface TransferSuccessProps {
    isOpen: boolean;
    onOpenChange: (value: boolean) => void;
}

export default function TransferSuccess({ isOpen, onOpenChange }: TransferSuccessProps) {

    return (
        <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent 
                className="max-w-sm px-6"
                onEscapeKeyDown={(e) => e.preventDefault()}
                onPointerDownOutside={(e) => e.preventDefault()}
            >
                <section className='pt-10 flex flex-col items-center gap-7'>
                    <Icons.success className='size-28' />
                    <div className="space-y-1 text-center px-3">
                        <h1 className='text-bold-32'>Transfer Successful</h1>
                        <p className='text-body-2 text-[#575A65]'>Hi jane, you have successfully transferred money into your nigerian account</p>
                    </div>
                    <div className="flex flex-col items-center gap-5">
                        <ClypButton 
                            classes="w-full shadow-none h-[50px] min-w-[200px] outline-none text-white"
                            text="View Receipt"    
                        />
                        <Link href="/balance/history">
                            <Button variant={"ghost"} className='text-orange'>
                                Go to History
                            </Button>
                        </Link>
                    </div>
                </section>
            </DialogContent>
        </Dialog>
    )
}
