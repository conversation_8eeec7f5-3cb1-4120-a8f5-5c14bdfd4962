"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/common/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/common/components/ui/card"
import { Input } from "@/common/components/ui/input"
import { Label } from "@/common/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/common/components/ui/select"
import { ArrowLeftRight, ArrowLeft, LoaderIcon } from "lucide-react"
import { useFetchWallets, useGetCurrenciesByWalletType, useGetConversionRate } from "../../_services/queries/queries"
import { TWallet } from "../../_services/interfaces"
import { WalletTypeEnum } from "../../[walletId]/history/_services/interfaces"
import { toastErrorMessage, toastSuccessMessage } from "@/common/lib/toast-message"
import { TSwa<PERSON><PERSON>rice } from "../../swap/_services/interfaces"  
import { useSwapContext } from "../../swap/_hooks/swap-context"

export default function ConversionPage() {
  const params = useParams()
  const router = useRouter()
  const walletId = params.walletId as string
  const [isLoading, setIsLoading] = useState(true)
  const [sourceWallet, setSourceWallet] = useState<TWallet | null>(null)
  const [targetCurrency, setTargetCurrency] = useState<string>("")
  const [amount, setAmount] = useState<string>("")
  const [estimatedAmount, setEstimatedAmount] = useState<string>("")
  const [isConverting, setIsConverting] = useState(false)
  
  const { data: walletsData } = useFetchWallets("", true)

  const { method } = useSwapContext()

  const { data: currenciesData } = useGetCurrenciesByWalletType(sourceWallet?.type ?? WalletTypeEnum.FIAT, !!sourceWallet)
  const currencies = currenciesData?.data || []

  const { data: conversionRateData } = useGetConversionRate(
    sourceWallet?.uid ?? '', 
    { 
      amount: 0, 
      from_currency: sourceWallet?.currency || '', 
      to_currency: '', 
      method
    }, 
    !!sourceWallet
  )
  const conversionRate = (typeof conversionRateData?.data === 'object') 
    ? (conversionRateData?.data as TSwapPrice)    
    : {} as TSwapPrice

  // Find the source wallet 
  useEffect(() => {
    const wallets = walletsData?.data || []
    if (wallets.length > 0) {
      const wallet = wallets.find(w => w.uid === walletId)
      if (wallet) {
        setSourceWallet(wallet)     
        }
        setIsLoading(false)
    }
  }, [walletsData, walletId])

  // Calculate conversion rate (mock implementation)
  const calculateConversion = () => {
    if (!amount || !targetCurrency || !sourceWallet) return

    const rate = conversionRate.rate
    const calculatedAmount = parseFloat(amount) * rate
    
    // Get precision from currencies data
    const targetCurrencyData = currencies.find(c => c.currency === targetCurrency)
    const precision = targetCurrencyData?.precision ?? 2
    
    setEstimatedAmount(calculatedAmount.toFixed(precision)) 
  }

  // Handle amount changes
  const handleAmountChange = (value: string) => {
    setAmount(value)
    setEstimatedAmount("")  // Reset estimated amount when input changes
  }

  // Handle target currency changes
  const handleTargetCurrencyChange = (value: string) => {
    setTargetCurrency(value)
    setEstimatedAmount("")  // Reset estimated amount when currency changes
  }

  // Execute conversion
  const handleConvert = async () => {
    if (!amount || !targetCurrency || !sourceWallet) return
    
    setIsConverting(true)
    
    try {
      // Simulate API call with delay
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Simulate successful conversion
      toastSuccessMessage(
        `Successfully converted ${amount} ${sourceWallet.currency} to ${estimatedAmount} ${targetCurrency}`
      )
      
      // Redirect back to balance page
      router.push('/balance')
    } catch {
      toastErrorMessage('Failed to convert currency')
    } finally {
      setIsConverting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <LoaderIcon className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!sourceWallet) {
    return (
      <div className="p-8">
        <Button onClick={() => router.back()} className="mb-4" variant="outline">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Card>
          <CardContent className="p-6">
            <p>Wallet not found</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-4 md:p-8">
      <Button onClick={() => router.back()} className="mb-6" variant="outline">
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back
      </Button>
      
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Convert {sourceWallet.currency}</CardTitle>
          <CardDescription>
            Convert your {sourceWallet.currency} to another currency
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Source Amount */}
          <div className="space-y-2">
            <Label htmlFor="amount">Amount to Convert ({sourceWallet.currency})</Label>
            <Input
              id="amount"
              type="number"
              placeholder="Enter amount"
              value={amount}
              onChange={(e) => handleAmountChange(e.target.value)}
              className="h-[56px]"
              min="0"
              step="any"
            />
            <p className="text-sm text-gray-500">
              Available: {Number(sourceWallet.balance).toLocaleString()} {sourceWallet.currency}
            </p>
          </div>
          
          {/* Currency Selector */}
          <div className="space-y-2">
            <Label htmlFor="currency">Target Currency</Label>
            <Select value={targetCurrency} onValueChange={handleTargetCurrencyChange}>
              <SelectTrigger id="currency" className="h-[56px]">
                <SelectValue placeholder="Select currency" />
              </SelectTrigger>
              <SelectContent>
                {currencies.map((currency) => (
                  <SelectItem key={currency.currency} value={currency.currency}>
                    {currency.currency}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          {/* Get Quote Button */}
          {!estimatedAmount && (
            <Button 
              className="w-full h-[56px]"
              onClick={calculateConversion}
              disabled={!amount || !targetCurrency || isConverting || parseFloat(amount) <= 0}
            >
              Get Quote
            </Button>
          )}
          
          {/* Conversion Quote */}
          {estimatedAmount && (
            <>
              <div className="bg-gray-50 p-4 rounded-md space-y-2">
                <div className="flex items-center justify-center gap-3">
                  <div className="text-center">
                    <p className="text-sm text-gray-500">From</p>
                    <p className="font-medium">{amount} {sourceWallet.currency}</p>
                  </div>
                  <ArrowLeftRight className="h-5 w-5 text-gray-400" />
                  <div className="text-center">
                    <p className="text-sm text-gray-500">To</p>
                    <p className="font-medium">{estimatedAmount} {targetCurrency}</p>
                  </div>
                </div>
              </div>
              
              {/* Convert Button */}
              <Button 
                className="w-full h-[56px] bg-paymate-green-600 hover:bg-initial text-white"
                onClick={handleConvert}
                disabled={isConverting}
              >
                {isConverting ? (
                  <LoaderIcon className="mr-2 h-5 w-5 animate-spin" />
                ) : null}
                {isConverting ? "Converting..." : "Convert"}
              </Button>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 