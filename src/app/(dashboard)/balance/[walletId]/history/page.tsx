"use client"
import Container from '@/common/components/custom/container'
import HistoryToolbar from './_components/history-toolbar'
import HistoryTable from './_components/history-table'
import BackButton from '@/common/components/custom/back-button'
import { useParams } from 'next/navigation'
import { useFetchWalletHistory } from '../../_services/queries/queries'
import { AlertCircle, LoaderIcon } from 'lucide-react'

export default function BalanceHistory() {
  const {walletId} = useParams<{walletId: string}>()

  const {data, isLoading, isError, error} = useFetchWalletHistory(walletId as string)

  // Display loading state
  if(isLoading) {
    return (
      <Container>
        <BackButton />
        <div className='flex justify-center items-center min-h-[50vh]'>
          <LoaderIcon className='size-10 animate-spin' />
        </div>
      </Container>
    )
  }

  // Display error state
  if(isError) {
    return (
      <Container>
        <BackButton />
        <div className='py-8 mt-4 border border-destructive text-destructive rounded-md p-4'>
          <div className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            <h3 className="font-medium">Error</h3>
          </div>
          <p className="mt-2 text-sm">
            {error instanceof Error ? error.message : 'Failed to load wallet history'}
          </p>
        </div>
      </Container>
    )
  }

  // Empty state for wallet history
  const walletHistory = data?.data ?? []
  const hasHistory = walletHistory.length > 0

  return (
    <Container>
      <section className='space-y-5'>
        <BackButton />

        <div className="pt-5 space-y-5">
          <h1 className="text-semibold-24">Balance History</h1>
          <HistoryToolbar />
          
          {!hasHistory ? (
            <div className='py-10 text-center text-muted-foreground'>
              No transaction history available for this wallet
            </div>
          ) : (
            <HistoryTable walletHistory={walletHistory} />
          )}
        </div>
      </section>
    </Container>
  )
}
