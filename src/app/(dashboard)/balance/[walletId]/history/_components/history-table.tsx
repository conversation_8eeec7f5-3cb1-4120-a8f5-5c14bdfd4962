"use client"

import { useState, useEffect } from 'react'
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  Row,
  Header,
  HeaderGroup
} from "@tanstack/react-table"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/common/components/ui/table"
import { Button } from "@/common/components/ui/button"
import { TWalletHistory } from '../_services/interfaces'
import { columns } from './history-columns'

// Create a context to share filter state between toolbar and table
import { createContext, useContext } from 'react'

export interface HistoryFilterContext {
  dateRange: { from: Date | undefined; to: Date | undefined };
  setDateRange: (range: { from: Date | undefined; to: Date | undefined }) => void;
  statusFilter: string;
  setStatusFilter: (status: string) => void;
  applyFilters: () => void;
}

export const HistoryFilterContext = createContext<HistoryFilterContext>({
  dateRange: { from: undefined, to: undefined },
  setDateRange: () => {},
  statusFilter: "all",
  setStatusFilter: () => {},
  applyFilters: () => {},
});

export const useHistoryFilter = () => useContext(HistoryFilterContext);

interface HistoryTableProps {
  walletHistory: TWalletHistory[];
}

export default function HistoryTable({ walletHistory = [] }: HistoryTableProps) {
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = useState({})
  
  // Filter states (will be controlled by the toolbar)
  const [filteredData, setFilteredData] = useState<TWalletHistory[]>([])
  const [appliedDateRange, setAppliedDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({ 
    from: undefined, 
    to: undefined 
  })
  const [appliedStatusFilter, setAppliedStatusFilter] = useState("all")
  
  // Initialize filteredData with walletHistory when component mounts
  useEffect(() => {
    setFilteredData(walletHistory);
  }, [walletHistory]);
  
  // This is for receiving external filter changes
  useEffect(() => {
    let filtered = [...walletHistory]
    
    // Apply date range filter
    if (appliedDateRange.from || appliedDateRange.to) {
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.created_at)
        if (appliedDateRange.from && appliedDateRange.to) {
          return itemDate >= appliedDateRange.from && itemDate <= appliedDateRange.to
        } else if (appliedDateRange.from) {
          return itemDate >= appliedDateRange.from
        } else if (appliedDateRange.to) {
          return itemDate <= appliedDateRange.to
        }
        return true
      })
    }
    
    // Apply status filter
    if (appliedStatusFilter !== "all") {
      filtered = filtered.filter(item => item.status === appliedStatusFilter)
    }
    
    setFilteredData(filtered)
  }, [appliedDateRange, appliedStatusFilter, walletHistory])
  
  // Provide filter context value
  const filterContextValue: HistoryFilterContext = {
    dateRange: appliedDateRange,
    setDateRange: setAppliedDateRange,
    statusFilter: appliedStatusFilter,
    setStatusFilter: setAppliedStatusFilter,
    applyFilters: () => {
      // This will be called by the "Filter" button in the toolbar
      // The useEffect will handle the actual filtering
    }
  }

  const table = useReactTable({
    data: filteredData,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  })

  return (
    <HistoryFilterContext.Provider value={filterContextValue}>
      <div className="space-y-4">
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup: HeaderGroup<TWalletHistory>) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header: Header<TWalletHistory, unknown>) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row: Row<TWalletHistory>) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No wallet history found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        <div className="flex items-center justify-end space-x-2 py-4">
          <div className="flex-1 text-sm text-muted-foreground">
            {table.getFilteredSelectedRowModel().rows.length} of{" "}
            {table.getFilteredRowModel().rows.length} row(s) selected.
          </div>
          <div className="space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              Next
            </Button>
          </div>
        </div>
      </div>
    </HistoryFilterContext.Provider>
  )
}
