"use client"

import React from 'react'
import DateRange from './date-range'
import ClypButton from '@/common/components/custom/clyp-button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/common/components/ui/select'
import { useHistoryFilter } from './history-table'
import { WalletStatusEnum } from '../_services/interfaces'

export default function HistoryToolbar() {
  const { setStatusFilter, applyFilters } = useHistoryFilter()

  const handleStatusChange = (value: string) => {
    setStatusFilter(value)
  }

  return (
    <div className='flex justify-end items-center gap-x-5'>
        <DateRange />

        <Select onValueChange={handleStatusChange}>
            <SelectTrigger className="w-[180px] h-[50px]">
            <SelectValue placeholder={"Status"} />
            </SelectTrigger>
            <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value={WalletStatusEnum.ACTIVE}>Active</SelectItem>
                <SelectItem value={WalletStatusEnum.INACTIVE}>Inactive</SelectItem>
                <SelectItem value={WalletStatusEnum.FROZEN}>Frozen</SelectItem>
            </SelectContent>
        </Select>

        <ClypButton
            text="Filter"
            classes="shadow-none h-[50px] min-w-[150px] outline-none text-white"
            onClick={applyFilters}
        />
    </div>
  )
}
