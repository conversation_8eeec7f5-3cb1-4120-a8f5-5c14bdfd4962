"use client"
import { But<PERSON> } from '@/common/components/ui/button'
import { Calendar } from '@/common/components/ui/calendar'
import { PopoverContent, PopoverTrigger } from '@/common/components/ui/popover'
import { Popover } from '@/common/components/ui/popover'
import { cn } from '@/common/lib/utils'
import { CalendarIcon } from 'lucide-react'
import { useState } from 'react'
import { format } from 'date-fns'

export default function DateRange() {
    const [fromDate, setFromDate] = useState<Date | undefined>(undefined)
    const [toDate, setToDate] = useState<Date | undefined>(undefined)
    return (
        <div>
            <section className='flex items-center'>
                <div>
                    <Popover>
                        <PopoverTrigger asChild>
                            <Button
                                variant={"outline"}
                                className={cn(
                                    "w-[200px] h-[50px] justify-start text-left font-normal rounded-r-none border-r-0",
                                    !fromDate && "text-muted-foreground"
                                )}
                            >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {fromDate ? format(fromDate, "PPP") : <span>Date from</span>}
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                            <Calendar
                                mode="single"
                                selected={fromDate}
                                onSelect={setFromDate}
                                captionLayout="dropdown"
                                initialFocus
                            />
                        </PopoverContent>
                    </Popover>
                </div>
                <div>
                    <Popover>
                        <PopoverTrigger asChild>
                            <Button
                                variant={"outline"}
                                className={cn(
                                    "w-[200px] h-[50px] justify-start text-left font-normal rounded-l-none",
                                    !toDate && "text-muted-foreground"
                                )}
                            >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {toDate ? format(toDate, "PPP") : <span>Date to</span>}
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                            <Calendar
                                mode="single"
                                selected={toDate}
                                onSelect={setToDate}
                                captionLayout="dropdown"
                                initialFocus
                            />
                        </PopoverContent>
                    </Popover>
                </div>
            </section>
        </div>
    )
}
