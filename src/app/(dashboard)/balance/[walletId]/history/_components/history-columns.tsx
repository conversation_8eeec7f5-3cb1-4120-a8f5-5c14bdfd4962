import { ColumnDef, Row, Column } from "@tanstack/react-table"
import { Button } from "@/common/components/ui/button"
import { ArrowUpDown } from "lucide-react"
import { cn, formatDate } from "@/common/lib/utils"
import { TWalletHistory, WalletOperationEnum, WalletStatusEnum } from '../_services/interfaces'

type TableRow = {row: Row<TWalletHistory>}
type TableColumn = {column: Column<TWalletHistory>}

export const columns: ColumnDef<TWalletHistory>[] = [
  {
    accessorKey: "date",
    header: ({ column }: TableColumn) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Date
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }: TableRow) => (
      <div className="text-regular-16 text-gray-2">
        {formatDate(row.getValue("date"), "/").split("/").reverse().join("/")}
      </div>
    ),
  },
  {
    accessorKey: "transaction_id",
    header: "Reference ID",
    cell: ({ row }: TableRow) => <div className="font-medium text-regular-16 text-gray-2">{row.original.transaction_id}</div>,
  },
  {
    accessorKey: "currency",
    header: ({ column }: TableColumn) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="text-regular-16 text-gray-2"
        >
          Currency
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }: TableRow) => <div className="font-medium text-regular-16 text-gray-2 text-center">
      {row.original.currency}
    </div>
  },
  {
    accessorKey: "amount",
    header: ({ column }: TableColumn) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="text-regular-16 text-gray-2"
        >
          Amount
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }: TableRow) => <div className="font-medium text-regular-16 text-gray-2 text-right">
      {Number(row.original.amount).toLocaleString()}
    </div>
  },
  {
    accessorKey: "fee",
    header: ({ column }: TableColumn) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="text-regular-16 text-gray-2"
        >
          Fee
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }: TableRow) => <div className="font-medium text-regular-16 text-gray-2 text-right">
      <span>{Number(row.original.fee).toLocaleString()}</span>
    </div>
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }: TableRow) => {
      const operation = row.getValue("type") as WalletOperationEnum
      return (
        <Button variant="ghost" className="text-regular-16 text-gray-2">
          {operation}
        </Button>
      )
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }: TableRow) => {
      const status = row.getValue("status") as WalletStatusEnum
      return (
        <div className="flex items-center justify-between gap-2">
          <Button 
            variant="ghost"
            className={cn(
              status === WalletStatusEnum.ACTIVE && "text-paymate-green-600",
              status === WalletStatusEnum.INACTIVE && "text-gray-500",
              status === WalletStatusEnum.FROZEN && "text-red-600",
              'text-regular-16 text-gray-2'
            )}
          >{status}</Button>
        </div>
      )
    },
  },
] 