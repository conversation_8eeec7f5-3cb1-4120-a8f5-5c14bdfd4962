import { BasicTableSchema } from "@/common/services/interfaces";

export enum WalletTypeEnum {
    FIAT = 'fiat',
    CRYPTO = 'crypto',
  }
  
  export enum WalletStatusEnum {
    ACTIVE = 'active',
    INACTIVE = 'inactive',
    FROZEN = 'frozen',
  }
  
  export enum WalletOwnerEnum {
    USER = 'user',
    ORGANIZATION = 'organization',
  }

  export enum PaymentGateway {
    CLYP = 'Clyp',
  }

export interface WalletMeta {
    wallet_type: WalletTypeEnum;
    wallet_owner: WalletOwnerEnum;
    currency_provider: PaymentGateway;
    currency_provider_account_id: string;
}

export enum HistoryActionEnum {
    INSERT = 'insert',
    UPDATE = 'update',
    DELETE = 'delete',
}

export enum WalletOperationEnum {
    DEPOSIT = 'deposit',
    WITHDRAW = 'withdraw',
  }
  

export interface TWalletHistory extends BasicTableSchema {
    transaction_id: string;
    account_uid: string;
    organization_uid: string;
    status: WalletStatusEnum;
    name: string;
    currency: string;
    amount: number;
    fee: number;
    meta: WalletMeta;
    wallet_id: string;
    action_type: HistoryActionEnum;
    action_by: string;
    type: WalletOperationEnum;
    date: string;
}