"use client"
import BackButton from '@/common/components/custom/back-button'
import Container from '@/common/components/custom/container'
import { Card } from '@/common/components/ui/card'
import FiatTransferForm from '../_components/fiat-transfer-form'
import { useParams } from 'next/navigation'
import CryptoTransferForm from '../_components/crypto-transfer-form'
import { useFetchWallets } from '../../_services/queries/queries'
import { useEffect, useState } from 'react'
import { TWallet } from '../../_services/interfaces'
import { WalletTypeEnum } from '../history/_services/interfaces'
import EllipsisLoader from '@/common/components/custom/ellipsis-loader'
import { useCookies } from '@/common/hooks/useCookies'

export default function WalletPage() {
  const { walletId } = useParams<{walletId: string}>()

  
  const [wallet, setWallet] = useState<TWallet | null>(null)

  const { getOrganizationUid } = useCookies();
  const organizationUid = getOrganizationUid();

  const {
    data: walletsData, 
    isFetching: fetchingWallet
  } = useFetchWallets(organizationUid!, !!organizationUid);

  useEffect(() => {
    if (walletsData?.data && walletId) {
      const foundWallet = walletsData.data.find(wallet => wallet.uid === walletId);
      
      if (foundWallet) {
        setWallet(foundWallet);
      }
    }
  }, [walletsData, walletId]);

  if (fetchingWallet) {
    return (
      <Container>
        <div className="min-h-[40vh] flex items-center justify-center">
          <EllipsisLoader />
        </div>
      </Container>
    );
  }

  if (!wallet) {
    return (
      <Container>
        <div className="space-y-10">
          <BackButton />
          <Card className="space-y-7 border-none shadow-none max-w-[450px]">
            <h1 className="text-bold-24 text-gray-2">Wallet Not Found</h1>
            <p className="text-gray-2 text-light-18">
              The wallet you&apos;re looking for couldn&apos;t be found.
            </p>
          </Card>
        </div>
      </Container>
    );
  }

  return (
    <Container>
        <div className='space-y-10'>
            <BackButton />

            <Card className='space-y-7 border-none shadow-none max-w-[450px]'>
                <h1 className="text-bold-24 text-gray-2">Fund Balance</h1>

                <div className='space-y-4'>
                    <p className="text-bold-20 text-orange text-base">
                      {wallet.type === "fiat" ? "Via Bank Transfer" : "Via Crypto Deposit"}
                    </p>
                    <p className="text-gray-2 text-light-18 text-xs">
                    Fund your balance by making a transfer to the account
                    number below.
                    </p>
                </div>
            </Card>
           
            {wallet.type === WalletTypeEnum.FIAT ? <FiatTransferForm wallet={wallet} /> : <CryptoTransferForm wallet={wallet} />}
        </div>
    </Container>
  )
}
