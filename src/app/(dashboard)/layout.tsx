"use client";
import { SidebarProvider, SidebarTrigger } from "@/common/components/ui/sidebar"
import { AppSidebar } from "./_components/app-sidebar"

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  return (
    <SidebarProvider>
      <AppSidebar />
      <main className="grow space-y-2">
        <header className="px-6 flex items-center gap-4 py-8">
            <SidebarTrigger className="flex-none" />
        </header>

        {children}
      </main>
    </SidebarProvider>
  )
}
