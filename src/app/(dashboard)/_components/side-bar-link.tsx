"use client"
import { Icons } from '@/common/components/custom/icon-templates';
import { TSidebarNavLink } from '../_services/interfaces';
import { usePathname, useRouter } from 'next/navigation';
import Link from 'next/link';
import { cn } from '@/common/lib/utils';
import { useLogoutMutation } from '@/app/(auth)/_services/queries/queries';
import { toastErrorMessage } from '@/common/lib/toast-message';
import { ACCESS_TOKEN_KEY } from '@/common/services/constants';
import { setCookie } from 'cookies-next';

export default function SidebarLink({link, isLogout}: {link: TSidebarNavLink, isLogout?: boolean}) {
    const Icon = Icons[link.icon as keyof typeof Icons];

    const pathname = usePathname();
    const router = useRouter()
    const {mutation} = useLogoutMutation()

    const handleClick = () => {
        if(!isLogout) return;

        mutation.mutateAsync(undefined).then(() => {
            setCookie(ACCESS_TOKEN_KEY, '', {
                maxAge: -1,
                secure: true,
            });
            router.push('/login')
        }).catch(() => {
            toastErrorMessage('Something went wrong')
        })
    }

    return (
        <Link
            href={link.href}
            className={cn(
                `flex items-center p-2 space-x-2 rounded-full text-semibold-16 py-4 px-5 group/sidelink`,
                pathname === link.href? isLogout? "bg-destructive text-white" : 
                isLogout? "bg-destructive text-white" : "bg-orange text-white" : "",
                isLogout? "hover:bg-destructive text-white" : "hover:bg-orange text-white"
            )}
            onClick={handleClick}
            aria-disabled={mutation.isLoading}
        >
            <Icon className={cn(
                pathname === link.href? "text-white": isLogout? "text-destructive" : "text-gray-2",
                "group-hover/sidelink:text-white"
            )} />
            <span className={cn(
                "group-hover/sidelink:text-white",
                pathname === link.href? "text-white": isLogout? "text-destructive" : "text-gray-2",
            )}>{link.title}</span>
        </Link>
    )
}
