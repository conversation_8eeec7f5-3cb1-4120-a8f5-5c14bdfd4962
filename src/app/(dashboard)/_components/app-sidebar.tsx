'use client'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  SidebarGroup,
  SidebarHeader,
} from "@/common/components/ui/sidebar"
import { Avatar, AvatarFallback, AvatarImage } from "@/common/components/ui/avatar"
import { sidebarfootlinks, sidebarlinks } from "../_services/constants"
import SidebarLink from "./side-bar-link"
import useOrganizationStore from "@/common/store/use-organization-store"

export function AppSidebar() {
  const { organization } = useOrganizationStore()

  return (
    <Sidebar className="px-0 bg-orange-50">
      <SidebarHeader className="">
        <div className="flex items-center justify-center h-14 py-14">
          {/* <Avatar>
                    <AvatarImage src="/images/user-ellipse.svg" />
                    <AvatarFallback>CN</AvatarFallback>
                </Avatar> */}
          <div className="ml-2 text-gray2">
            <span className="text-regular-16 font-semibold">{organization?.name}</span>
            <div className="text-regular-14 truncate whitespace-nowrap">Org ID: {organization?.uid}</div>
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup className="gap-y-2">
          {sidebarlinks.map((link, index) => (
            <SidebarLink key={index} link={link} />
          ))}
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        {sidebarfootlinks.map((link, index) => (
          <SidebarLink key={index} link={link} isLogout />
        ))}
      </SidebarFooter>
    </Sidebar>
  )
}
