import { TSidebarNavLink } from "./interfaces";

export const sidebarlinks: TSidebarNavLink[] = [
    {
        title: "Dashboard",
        icon: "dashboard",
        href: "/dashboard",
    },
    {
        title: "Transactions",
        icon: "transfer",
        href: "/transactions",
    },
    {
        title: "Customers",
        icon: "users",
        href: "/customers",
    },
    {
        title: "Balance",
        icon: "wallet",
        href: "/balance",
    },
    {
        title: "Payouts",
        icon: "moneys",
        href: "/payouts",
    },
    {
        title: "Payment Links",
        icon: "link",
        href: "/payment-links",
    },
    {
        title: "Invoices",
        icon: "reciept",
        href: "/invoices",
    },
    {
        title: "Transfers",
        icon: "send",
        href: "/transfers",
    },
    {
        title: "Settings",
        icon: "settings",
        href: "/settings",
    },
];

export const sidebarfootlinks: TSidebarNavLink[] = [
    {
        title: "Logout",
        icon: "powerOff",
        href: "#",
    },
]