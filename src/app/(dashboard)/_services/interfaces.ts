import { INDUSTRY, R<PERSON><PERSON><PERSON><PERSON>RY_COMPLIANCE_STATUS } from "@/app/create-organization/_services/interfaces";
import { REGULATORY_COMPLIANCE_DOCUMENTS_SCHEMA } from "@/app/create-organization/_services/validations";
import { Icons } from "@/common/components/custom/icon-templates";
import { BasicTableSchema } from "@/common/services/interfaces";
import { z } from "zod";

export type TSidebarNavLink = {
    title: string;
    icon: keyof typeof Icons;
    href: string;
    children?: TSidebarNavLink[];
    isLogout?: boolean
};

export type TSearchBarProps = {
    placeholder: string;
    onSearch: (query: string) => void;
}   

export interface TOrganization extends BasicTableSchema {
    name: string;
    address: string;
    industry: INDUSTRY;
    regulatory_compliance_docs: z.infer<typeof REGULATORY_COMPLIANCE_DOCUMENTS_SCHEMA>;
    regulatory_compliance_status: REGULATORY_COMPLIANCE_STATUS;
    regulatory_compliance_review_note: string | null;
    complaince_info_updated_at: Date | null;
}
