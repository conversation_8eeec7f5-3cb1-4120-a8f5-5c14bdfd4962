/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable unused-imports/no-unused-vars */
"use client"
import BackButton from '@/common/components/custom/back-button'
import { Form, FormControl, FormField, FormItem, FormLabel } from '@/common/components/ui/form'
import { useForm } from 'react-hook-form'
import { TCreateCustomer } from '../_services/interfaces'
import PlaceholderFloatInput from '@/common/components/custom/placeholder-float-input'
import { zodResolver } from '@hookform/resolvers/zod'
import { CreateCustomerSchema } from '../_services/validations'
import ClypButton from '@/common/components/custom/clyp-button'
import { useCreateCustomerMutation, useUpdateCustomerMutation, useGetCustomerQuery } from '../_services/queries/queries';
import { toastErrorMessage, toastSuccessMessage } from '@/common/lib/toast-message'
import { useRouter } from 'next/navigation';
import { useEffect } from 'react'
import { useSearchParams } from 'next/navigation';

const DEFAULT_VALUES = {
  first_name: "",
  last_name: "",
  email: "",
  phone_number: "",
}

export default function NewCustomer() {
  const { mutation: createMutation } = useCreateCustomerMutation();
  const { mutation: updateMutation } = useUpdateCustomerMutation();
  const router = useRouter();
  const searchParams = useSearchParams();
  const customerId = searchParams.get('id');
  const isEdit = !!customerId;
  const { data: customerData, isLoading: loadingCustomer } = useGetCustomerQuery(customerId || '', isEdit);

  const form = useForm<TCreateCustomer>({
    defaultValues: DEFAULT_VALUES,
    resolver: zodResolver(CreateCustomerSchema),
  });

  useEffect(() => {
    if (isEdit && customerData?.data) {
      form.reset({
        first_name: customerData.data.first_name,
        last_name: customerData.data.last_name,
        email: customerData.data.email,
        phone_number: customerData.data.phone_number,
      });
    }
  }, [isEdit, customerData, form]);

  const onSubmitHandler = async (data: TCreateCustomer) => {
    if (isEdit && customerId) {
      return updateMutation.mutateAsync({ customer_uid: customerId, data }).then((response) => {
        toastSuccessMessage('Customer updated successfully');
        router.push('/customers');
      }).catch((error) => {
        toastErrorMessage(error.message);
      });
    } else {
      return createMutation.mutateAsync(data).then((response) => {
        form.reset(DEFAULT_VALUES);
        toastSuccessMessage(response.message);
        router.push('/customers');
      }).catch((error) => {
        toastErrorMessage(error.message);
      });
    }
  };

  return (
    <div className='px-6 space-y-6'>
      <BackButton />
      <h1 className='text-bold-24 text-gray-2'>{isEdit ? 'Edit customer' : 'Add new customer'}</h1>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmitHandler)}
          className='max-w-[900px] pb-20'
        >
          <div className="flex flex-col md:grid grid-cols-2 gap-x-4 gap-y-4 md:gap-y-10">
            <FormField
              control={form.control}
              name="first_name"
              disabled={createMutation.isLoading || updateMutation.isLoading || loadingCustomer}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>First name</FormLabel>
                  <FormControl>
                    <PlaceholderFloatInput
                      placeholder='Customer first name'
                      field={field}
                      name='first_name'
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="last_name"
              disabled={createMutation.isLoading || updateMutation.isLoading || loadingCustomer}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Last name</FormLabel>
                  <FormControl>
                    <PlaceholderFloatInput
                      placeholder='Customer last name'
                      field={field}
                      name='last_name'
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              disabled={createMutation.isLoading || updateMutation.isLoading || loadingCustomer}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Customer email address</FormLabel>
                  <FormControl>
                    <PlaceholderFloatInput
                      placeholder='Customer email address'
                      field={field}
                      name='email'
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="phone_number"
              disabled={createMutation.isLoading || updateMutation.isLoading || loadingCustomer}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone number</FormLabel>
                  <FormControl>
                    <PlaceholderFloatInput
                      placeholder='Phone number'
                      field={field}
                      name='phone_number'
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <div className="col-span-2 md:col-span-1 md:grid grid-cols-2 gap-4">
              <ClypButton
                text={isEdit ? 'Update Customer' : 'Add Customer'}
                type="submit"
                classes='text-medium-18 text-white md:h-[60px] w-full'
                disabled={!form.formState.isValid || createMutation.isLoading || updateMutation.isLoading || loadingCustomer}
                isLoading={createMutation.isLoading || updateMutation.isLoading}
              />
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
