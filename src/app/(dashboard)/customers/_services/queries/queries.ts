"use client"
import { useMutation, UseMutationResult, useQuery, useQueryClient } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { TResponse } from "@/common/services/interfaces";
import { TCreateCustomer, TCustomer } from "../interfaces";
import { CreateCustomer, FetchCustomers, DeleteCustomer, GetCustomer, UpdateCustomer } from "../api";
import { FETCH_CUSTOMERS } from "./query-keys";


export const useCreateCustomerMutation = () => {
    const qc = useQueryClient();

    const mutation: UseMutationResult<
        TResponse<TCustomer>,
        AxiosError,
        TCreateCustomer
    > = useMutation({
        mutationKey: ["create-customer"],
        mutationFn: CreateCustomer,
        onSuccess: () => {
            qc.refetchQueries({
                queryKey: [FETCH_CUSTOMERS]
            })
        }
    });

    return { mutation, data: mutation.data }
}

export const useFetchCustomers = () => {
    const { data, isLoading } = useQuery({
        queryKey: [FETCH_CUSTOMERS],
        queryFn: FetchCustomers
    })

    return { data, isLoading }
}

export const useDeleteCustomerMutation = () => {
    const qc = useQueryClient();
    const mutation = useMutation({
        mutationKey: ["delete-customer"],
        mutationFn: DeleteCustomer,
        onSuccess: () => {
            qc.refetchQueries({ queryKey: [FETCH_CUSTOMERS] });
        },
    });
    return { mutation };
};

export const useGetCustomerQuery = (customer_uid: string, enabled = true) => {
    return useQuery({
        queryKey: ["get-customer", customer_uid],
        queryFn: () => GetCustomer(customer_uid),
        enabled: !!customer_uid && enabled,
    });
};

export const useUpdateCustomerMutation = () => {
    const qc = useQueryClient();
    const mutation = useMutation({
        mutationKey: ["update-customer"],
        mutationFn: ({ customer_uid, data }: { customer_uid: string; data: Partial<TCreateCustomer> }) =>
            UpdateCustomer(customer_uid, data),
        onSuccess: () => {
            qc.invalidateQueries({ queryKey: [FETCH_CUSTOMERS] });
        },
    });
    return { mutation };
};

