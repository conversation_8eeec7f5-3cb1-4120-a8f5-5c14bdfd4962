import { apiRequest } from "@/common/lib/api-request";
import { TResponse } from "@/common/services/interfaces";
import { TCreateCustomer, TCustomer } from "./interfaces";
import { getRoutes } from "@/common/services/constants";


export const FetchCustomers = async () =>
    await apiRequest<undefined, TResponse<TCustomer[]>>({
        path: getRoutes.getCustomers().path,
        method: getRoutes.getCustomers().method,
    });

export const CreateCustomer = async (data: TCreateCustomer) =>
    await apiRequest<TCreateCustomer, TResponse<TCustomer>>({
        path: getRoutes.createCustomer().path,
        method: getRoutes.createCustomer().method,
        payload: data
    })

export const DeleteCustomer = async (customer_uid: string) =>
    await apiRequest<undefined, TResponse<null>>({
        path: getRoutes.deleteCustomer(customer_uid).path,
        method: getRoutes.deleteCustomer(customer_uid).method,
    });

export const GetCustomer = async (customer_uid: string) =>
    await apiRequest<undefined, TResponse<TCustomer>>({
        path: getRoutes.getCustomer(customer_uid).path,
        method: getRoutes.getCustomer(customer_uid).method,
    });

export const UpdateCustomer = async (customer_uid: string, data: Partial<TCreateCustomer>) =>
    await apiRequest<Partial<TCreateCustomer>, TResponse<TCustomer>>({
        path: getRoutes.updateCustomer(customer_uid).path,
        method: getRoutes.updateCustomer(customer_uid).method,
        payload: data,
    });
