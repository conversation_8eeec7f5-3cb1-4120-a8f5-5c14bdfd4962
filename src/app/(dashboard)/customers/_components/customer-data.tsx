import React from "react";
import { DataTable, DataTableColumn } from "@/common/components/ui/DataTable";
import { useTable } from "@/common/hooks/useTable";
import { useFetchCustomers } from "../_services/queries/queries";
import { TCustomer } from "../_services/interfaces";
import RedirectToAddCustomer from "./redirect-to-add";
import { useRouter } from 'next/navigation';
import {  PenBox, Trash2 } from "lucide-react";
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/common/components/ui/dialog';
import { useDeleteCustomerMutation } from '../_services/queries/queries';
import { useState } from 'react';
import ClypButton from "@/common/components/custom/clyp-button";

export default function CustomerData() {
  const { data, isLoading } = useFetchCustomers();
  const customers: TCustomer[] = data?.data || [];
  const router = useRouter();
  const { mutation: deleteMutation } = useDeleteCustomerMutation();
  const [open, setOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<TCustomer | null>(null);

  const handleDeleteClick = (customer: TCustomer) => {
    setSelectedCustomer(customer);
    setOpen(true);
  };

  const handleConfirmDelete = () => {
    if (selectedCustomer) {
      deleteMutation.mutate(selectedCustomer.uid, {
        onSuccess: () => setOpen(false),
        onError: () => setOpen(false),
      });
    }
  };

  const columns: DataTableColumn<TCustomer>[] = [
    {
      label: "Full Name",
      accessor: "first_name",
      sortable: true,
      render: (row) => `${row.first_name} ${row.last_name}`,
    },
    {
      label: "Email",
      accessor: "email",
      sortable: true,
    },
    {
      label: "Phone Number",
      accessor: "phone_number",
      sortable: true,
    },
    {
      label: "Date Added",
      accessor: "created_at",
      sortable: true,
      render: (row) => new Date(row.created_at).toLocaleDateString(),
    },
    {
      label: "",
      accessor: "action",
      render: (row) => (
        <div className="w-fit flex items-center justify-items-center rounded-full bg-orange/10 p-2 text-orange hover:bg-orange/20">
          <span className="sr-only">edit</span>
          <button
            onClick={e => {
              e.stopPropagation();
              router.push(`/customers/new-customer?id=${row.uid}`);
            }}
            className="focus:outline-none"
            type="button"
          >
            <PenBox size={16} />
          </button>
        </div>
      )
    },
    {
      label: '',
      accessor: 'actions',
      render: (row) => (
        <Dialog open={open && selectedCustomer?.uid === row.uid} onOpenChange={(val) => { setOpen(val); if (!val) setSelectedCustomer(null); }}>
          <DialogTrigger asChild>
            <button
              className="rounded-full bg-orange/10 p-2 text-orange hover:bg-orange/20"
              onClick={e => { e.stopPropagation(); handleDeleteClick(row); }}
            >
              <span className="sr-only">Delete</span>
              <Trash2 size={16} />
            </button>
          </DialogTrigger>
          <DialogContent onClick={e => e.stopPropagation()}>
            <DialogHeader>
              <DialogTitle>Delete Customer</DialogTitle>
            </DialogHeader>
            <p>Are you sure you want to delete this customer?</p>
            <DialogFooter>
              <ClypButton
                className={`px-4 py-2 rounded bg-gray-200 hover:bg-gray-300`}
                onClick={e => { e.stopPropagation(); setOpen(false); }}
                type="button"
                text="cancel"
                variant="outline"
                disabled={deleteMutation.isLoading}
              />
              <ClypButton
                className="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-700"
                onClick={e => { e.stopPropagation(); handleConfirmDelete(); }}
                type="button"
                disabled={deleteMutation.isLoading}
                isLoading={deleteMutation.isLoading}
                text={`${deleteMutation.isLoading ? 'Deleting...' : 'Delete'}`}
              />
            </DialogFooter>
          </DialogContent>
        </Dialog>
      ),
    },
  ];

  const table = useTable({
    data: customers,
    initialPage: 1,
    pageSize: 8,
  });

  // Row click handler
  const handleRowClick = (customer: TCustomer) => {
    router.push(`/customers/${customer.uid}`);
  };

  return (
    <section className="space-y-6 py-10">
      <header className="flex items-center justify-between">
        <h1 className="text-bold-20 text-gray-black">{customers.length} Customers</h1>
        <RedirectToAddCustomer classes="bg-orange w-max py-5" text="Add new customer" />
      </header>
      <div className="mx-auto space-y-4">
        <DataTable
          columns={columns}
          data={table.data}
          page={table.page}
          pageSize={table.pageSize}
          total={table.total}
          onPageChange={table.setPage}
          sortBy={table.sortBy}
          sortDirection={table.sortDirection}
          onSortChange={table.onSortChange}
          loading={isLoading}
          onRowClick={handleRowClick}
        />
      </div>
    </section>
  );
}
