"use client"
import EmptyCustomers from './empty-customers'
import { useFetchCustomers } from '../_services/queries/queries'
import { LoaderPinwheel } from 'lucide-react';
import CustomerData from './customer-data';

export default function Customers() {
    const {data, isLoading} = useFetchCustomers();

    if(isLoading) {
       return <div className="min-h-[60vh] flex items-center justify-center">
        <LoaderPinwheel className="size-24 animate-spin text-paymate-green-50" />
        </div>
    }

    const customers = data?.data || []

    if(customers.length === 0) {
        return  <EmptyCustomers />
    }

    return <CustomerData />
}
