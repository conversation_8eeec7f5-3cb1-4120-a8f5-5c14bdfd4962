"use client";

import ClypButton from '@/common/components/custom/clyp-button'
import { cn } from '@/common/lib/utils'
import { useRouter } from 'next/navigation'
export default function RedirectToAddCustomer({classes, text}: {
    classes?: string
    text?: string
}) {
    const router = useRouter();

    return (
        <ClypButton
            classes={cn('w-full bg-gray-2 text-medium-18 text-white', classes)}
            text={text ?? "+ Add new customer"}
            onClick={() => router.push('/customers/new-customer')}
        />
    )
}
