import { Icons } from '@/common/components/custom/icon-templates'
import React from 'react'
import RedirectToAddCustomer from './redirect-to-add'

export default function EmptyCustomers() {
  return (
    <div className='py-10 flex flex-col items-center justify-center gap-3 md:gap-4 max-w-[480px] mx-auto'>
        <Icons.profileCircle className='size-32 md:size-48' />
        <h2 className='text-gray-black md:pb-8 text-medium-16 md:text-bold-24'>You do not have any customers yet, you can add new customers by clicking the button below.</h2>
        <RedirectToAddCustomer classes="py-6 md:py-7" />
    </div>
  )
}
