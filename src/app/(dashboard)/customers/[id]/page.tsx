"use client";
import React from "react";
import { useParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/common/lib/api-request";
import { getRoutes } from "@/common/services/constants";
// import { Card, CardContent, CardHeader, CardTitle } from "@/common/components/ui/card";
import { LoaderPinwheel } from "lucide-react";
// import { cn } from "@/common/lib/utils";
import { TCustomer } from "../_services/interfaces";
import BackButton from "@/common/components/custom/back-button";
type CustomerResponse = { data: TCustomer };

export default function CustomerDetailPage() {
  const params = useParams();
  const customerId = params?.id as string;

  const { data, isLoading, error } = useQuery<CustomerResponse>({
    queryKey: ["customer", customerId],
    queryFn: async () => {
      return apiRequest({
        path: getRoutes.getCustomer(customerId).path,
        method: getRoutes.getCustomer(customerId).method,
      });
    },
    enabled: !!customerId,
  });

  // console.log("data", data)

  if (isLoading) {
    return (
      <div className="min-h-[60vh] flex items-center justify-center">
        <LoaderPinwheel className="size-24 animate-spin text-paymate-green-50" />
      </div>
    );
  }

  if (error || !data?.data) {
    return (
      <div className="min-h-[60vh] flex items-center justify-center text-red-500 text-lg font-semibold">
        Customer not found.
      </div>
    );
  }

  const customer = data.data;

  return (
    <>
    <div className="px-6 max-w-3xl mx-auto">
    <BackButton />

      <div className="mt-4 mb-6 rounded-full w-full bg-gray-6 p-5 text-black flex items-center justify-between">
        <span className="">Field</span>
        <span className="">Detail</span>
      </div>

      <div className="space-y-6 pt-0">
        <div className="flex flex-col gap-2 space-y-6 even:border-b-2">

          <div className="flex items-center justify-between">
            <span className="font-medium text-gray-600">Email:</span>
            <span className="text-gray-900">{customer.first_name} {customer.last_name}</span>
          </div>

          <div className="flex items-center justify-between">
            <span className="font-medium text-gray-600">Email:</span>
            <span className="text-gray-900">{customer.email}</span>
          </div>

          <div className="flex items-center justify-between">
            <span className="font-medium text-gray-600">Phone Number:</span>
            <span className="text-gray-900">{customer.phone_number}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="font-medium text-gray-600">Date Added:</span>
            <span className="text-gray-900">{new Date(customer.created_at).toLocaleDateString()}</span>
          </div>
        </div>
      </div>
    </div>
    </>

  );
}
