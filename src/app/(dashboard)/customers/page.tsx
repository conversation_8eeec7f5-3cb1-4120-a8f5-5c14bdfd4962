// import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/common/components/ui/tabs'
import Customers from './_components/customers'
import EmptyBlacklist from './_components/empty-blacklist'
import CustomTabs from '@/common/components/custom/custom-tabs'
import { CustomerTabEnums } from './_services/enum'
// import LoadingWallet from '../balance/swap/_components/loading-wallet'
import { Suspense } from 'react'


export default function CustomerPage() {
  return (
    <Suspense>
      <div className='px-6'>

      <CustomTabs
        defaultValue={CustomerTabEnums.ALL}
        property='type'
        tabs={[
          {
            label: 'All Customers',
            value: CustomerTabEnums.ALL,
            content: <Customers />
          },
          {
            label: 'Blacklisted Customers',
            value: CustomerTabEnums.BLACKLIST,
            content: <EmptyBlacklist />
          }
        ]}
      />

      </div>

      {/* <Tabs defaultValue="all" className="w-full">
        <TabsList>
          <TabsTrigger value="all">All Customers</TabsTrigger>
          <TabsTrigger value="blacklist">Blacklisted Customers</TabsTrigger>
        </TabsList>
        <TabsContent value="all">
          <Customers />
        </TabsContent>
        <TabsContent value="blacklist">
          <EmptyBlacklist />
        </TabsContent>
      </Tabs> */}
    </Suspense>
  )
}
