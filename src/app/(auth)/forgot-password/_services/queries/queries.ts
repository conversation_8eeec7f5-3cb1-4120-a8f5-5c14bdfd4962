import { useMutation } from '@tanstack/react-query';
import { apiRequest } from '@/common/lib/api-request';
import { getRoutes } from '@/common/services/constants';
import {
  TForgotPasswordRequest,
  TForgotPasswordVerifyCode,
  TForgotPasswordReset,
} from '../interfaces';
import { TResponse } from '@/common/services/interfaces';

// Send forgot password email
export function useForgotPasswordMutation() {
  const mutation = useMutation({
    mutationFn: (data: TForgotPasswordRequest) =>
      apiRequest<TForgotPasswordRequest, TResponse<null>>({
        path: getRoutes.forgotPassword().path,
        method: getRoutes.forgotPassword().method,
        payload: data,
      }),
  });
  return { mutation };
}

// Verify code sent to email
export function useVerifyForgotPasswordCodeMutation() {
  const mutation = useMutation({
    mutationFn: (data: TForgotPasswordVerifyCode) =>
      apiRequest<TForgotPasswordVerifyCode, TResponse<null>>({
        path: getRoutes.forgotPassword().path + '/verify', // Adjust if you have a specific verify endpoint
        method: 'POST',
        payload: data,
      }),
  });
  return { mutation };
}

// Reset password
export function useResetForgotPasswordMutation() {
  const mutation = useMutation({
    mutationFn: (data: TForgotPasswordReset) =>
      apiRequest<TForgotPasswordReset, TResponse<null>>({
        path: getRoutes.resetPassword().path,
        method: getRoutes.resetPassword().method,
        payload: data,
      }),
  });
  return { mutation };
} 