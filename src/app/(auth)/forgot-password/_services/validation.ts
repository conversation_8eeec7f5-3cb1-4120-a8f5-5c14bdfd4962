import { z } from 'zod';

export const ForgotPasswordRequestSchema = z.object({
    email: z.string().email({ message: 'Please enter a valid email address' }),
});

export const ForgotPasswordVerifyCodeSchema = z.object({
    code: z.string().min(4, { message: 'Code must be at least 4 digits' }),
});

export const ForgotPasswordResetSchema = z.object({
    password: z
        .string()
        .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\w\s]).{8,}$/, {
            message:
                'Password must contain at least an uppercase letter, a lowercase letter, a number and a special character',
        }),
    confirm_password: z.string(),
}).refine((data) => data.password === data.confirm_password, {
    message: 'Passwords do not match',
    path: ['confirm_password'],
});