import AuthPageLayout from '@/common/components/custom/auth-layout';
import React from 'react';
import ForgotPasswordForm from './_components/forgot-password-form';
import { IndexPageProps } from '@/common/services/interfaces';
import VerifyCode from './_components/verify-code';
import SuccessModal from './_components/forgot-password-success';
import CreateNewPassword from './_components/create-new-password';
import { ForgotPasswordProgress } from './_services/interfaces';

export default async function ForgotPasswordPage({ searchParams }: IndexPageProps) {
    const search = await searchParams;
    const progress = search.progress as ForgotPasswordProgress;

    const progressComponents = {
        [ForgotPasswordProgress.VERIFY_EMAIL]: <VerifyCode />,
        [ForgotPasswordProgress.CREATE_NEW_PASSWORD]: <CreateNewPassword />,
        [ForgotPasswordProgress.SUCCESS]: <SuccessModal
            title="Password Reset Successful!"
            description='Your password has been reset successfully. Click proceed to login.'
            route={"/login"}
            btnText='Proceed to login'
        />,
    };
    
    return (
        <AuthPageLayout>
            {progressComponents[progress] || <ForgotPasswordForm />}
        </AuthPageLayout>
    );
} 