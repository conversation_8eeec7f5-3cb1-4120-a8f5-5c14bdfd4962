"use client";
import AuthFormLayout from '@/common/components/custom/auth-form-layout';
import { Form, FormControl, FormDescription, FormField, FormItem } from '@/common/components/ui/form';
import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ForgotPasswordResetSchema } from '../_services/validation';
import { TForgotPasswordReset } from '../_services/interfaces';
import ClypButton from '@/common/components/custom/clyp-button';
import { PasswordInput } from '@/common/components/custom/password-input';
import { useResetForgotPasswordMutation } from '../_services/queries/queries';
import { toastErrorMessage, toastSuccessMessage } from '@/common/lib/toast-message';

export default function CreateNewPassword() {
    const { mutation } = useResetForgotPasswordMutation();
    const form = useForm<TForgotPasswordReset>({
        defaultValues: { password: '', confirm_password: '' },
        mode: 'onChange',
        resolver: zodResolver(ForgotPasswordResetSchema),
    });

    const onSubmitHandler = async (data: TForgotPasswordReset) => {
        return mutation.mutateAsync(data)
            .then(() => {
                toastSuccessMessage('Password reset successful.');
            })
            .catch((error) => {
                toastErrorMessage(error.message);
            });
    };

    return (
        <AuthFormLayout title="Create New Password" description="Enter your new password.">
            <Form {...form}>
                <form className="space-y-5" onSubmit={form.handleSubmit(onSubmitHandler)}>
                    <FormField
                        control={form.control}
                        name="password"
                        render={({ field }) => (
                            <FormItem>
                                <FormControl>
                                    <PasswordInput
                                        {...field}
                                        placeholder="New Password"
                                        className="text-regular-16 focus:outline-none! h-[52px] md:h-[64px]"
                                    />
                                </FormControl>
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="confirm_password"
                        render={({ field }) => (
                            <FormItem>
                                <FormControl>
                                    <PasswordInput
                                        {...field}
                                        placeholder="Re-enter New Password"
                                        className="text-regular-16 focus:outline-none! h-[52px] md:h-[64px]"
                                    />
                                </FormControl>
                            </FormItem>
                        )}
                    />
                    <FormDescription className='text-medium-10 md:text-medium-14'>
                        Must contain at least an uppercase letter, a lowercase letter, a number and a special character
                    </FormDescription>
                    <ClypButton
                        classes="w-full h-[55px] rounded-full"
                        text="Reset Password"
                        type="submit"
                        disabled={mutation.isLoading || !form.formState.isValid}
                        isLoading={mutation.isLoading}
                    />
                </form>
            </Form>
        </AuthFormLayout>
    );
} 