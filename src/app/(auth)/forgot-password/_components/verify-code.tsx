"use client";
import AuthFormLayout from '@/common/components/custom/auth-form-layout';
import { Form, FormControl, FormDescription, FormField, FormItem } from '@/common/components/ui/form';
import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ForgotPasswordVerifyCodeSchema } from '../_services/validation';
import { TForgotPasswordVerifyCode } from '../_services/interfaces';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/common/components/ui/input-otp';
import { REGEXP_ONLY_DIGITS } from 'input-otp';
import { useVerifyForgotPasswordCodeMutation } from '../_services/queries/queries';
import { toastErrorMessage, toastSuccessMessage } from '@/common/lib/toast-message';

export default function VerifyCode() {
    const { mutation } = useVerifyForgotPasswordCodeMutation();
    const form = useForm<TForgotPasswordVerifyCode>({
        defaultValues: { code: '' },
        mode: 'onChange',
        resolver: zodResolver(ForgotPasswordVerifyCodeSchema),
    });

    const onSubmitHandler = async (data: TForgotPasswordVerifyCode) => {
        return mutation.mutateAsync(data)
            .then(() => {
                toastSuccessMessage('Code verified. You can now reset your password.');
            })
            .catch((error) => {
                toastErrorMessage(error.message);
            });
    };

    return (
        <div className='pt-10'>
            <AuthFormLayout title='Input Code' description='Input the code sent to your email address.'>
                <Form {...form}>
                    <form className="space-y-5 h-full overflow-hidden" onSubmit={form.handleSubmit(onSubmitHandler)}>
                        <FormField
                            control={form.control}
                            name="code"
                            render={({ field }) => (
                                <FormItem>
                                    <FormControl>
                                        <InputOTP maxLength={6} pattern={REGEXP_ONLY_DIGITS} {...field}>
                                            <InputOTPGroup className="cp-otp-group">
                                                <InputOTPSlot index={0} />
                                                <InputOTPSlot index={1} />
                                                <InputOTPSlot index={2} />
                                                <InputOTPSlot index={3} />
                                                <InputOTPSlot index={4} />
                                                <InputOTPSlot index={5} />
                                            </InputOTPGroup>
                                        </InputOTP>
                                    </FormControl>
                                </FormItem>
                            )}
                        />
                        <div className="">
                            <FormDescription>
                                {"Didn't get the code? "}
                                <span className="text-green-600 font-bold ml-3 cursor-pointer">Resend code</span>
                            </FormDescription>
                        </div>
                    </form>
                </Form>
            </AuthFormLayout>
        </div>
    );
} 