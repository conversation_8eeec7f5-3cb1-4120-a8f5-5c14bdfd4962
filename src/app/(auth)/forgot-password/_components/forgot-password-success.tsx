"use client"

import React from 'react';
import { useRouter } from 'next/navigation';
import ClypButton from '@/common/components/custom/clyp-button';

interface SuccessModalProps {
    title: string;
    description: string;
    route: string;
    btnText: string;
}

export default function SuccessModal({ title, description, route, btnText }: SuccessModalProps) {
    const router = useRouter();
    return (
        <div className="flex flex-col items-center justify-center py-10">
            <h2 className="text-2xl font-bold mb-4">{title}</h2>
            <p className="mb-6 text-center">{description}</p>
            <ClypButton
                text={btnText}
                classes="w-full h-[55px] rounded-full"
                onClick={() => router.push(route)}
            />
        </div>
    );
} 