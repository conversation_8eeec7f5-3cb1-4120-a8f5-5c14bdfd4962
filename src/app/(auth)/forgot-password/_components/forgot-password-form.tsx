"use client";
import AuthFormLayout from '@/common/components/custom/auth-form-layout';
import { Form, FormControl, FormDescription, FormField, FormItem } from '@/common/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ForgotPasswordRequestSchema } from '../_services/validation';
import { TForgotPasswordRequest } from '../_services/interfaces';
import ClypButton from '@/common/components/custom/clyp-button';
import PlaceholderFloatInput from '@/common/components/custom/placeholder-float-input';
import { useForgotPasswordMutation } from '../_services/queries/queries';
import { toastErrorMessage, toastSuccessMessage } from '@/common/lib/toast-message';
import Link from 'next/link';

export default function ForgotPasswordForm() {
    const { mutation } = useForgotPasswordMutation();
    const form = useForm<TForgotPasswordRequest>({
        defaultValues: { email: '' },
        mode: 'onChange',
        resolver: zodResolver(ForgotPasswordRequestSchema),
    });

    const onSubmitHandler = async (data: TForgotPasswordRequest) => {
        return mutation.mutateAsync(data)
            .then((res) => {
                toastSuccessMessage(res.message || 'Verification code sent to your email');
            })
            .catch((error) => {
                toastErrorMessage(error.message || 'An error occur');
            });
    };

    return (
        <AuthFormLayout title="Forgot Password" description="Enter your email to reset your password.">
            <Form {...form}>
                <form className="space-y-5" onSubmit={form.handleSubmit(onSubmitHandler)}>
                    <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                            <FormItem>
                                <FormControl>
                                    <PlaceholderFloatInput
                                        placeholder="Email Address"
                                        field={field}
                                        name="email"
                                    />
                                </FormControl>
                                <FormDescription>Must be a valid email address</FormDescription>
                            </FormItem>
                        )}
                    />
                    <ClypButton
                        classes="w-full h-[55px] rounded-full"
                        text="Send Code"
                        type="submit"
                        disabled={mutation.isLoading || !form.formState.isValid}
                        isLoading={mutation.isLoading}
                    />

                    <div className="form-redirect-text">
                        <Link href="/login" className="link">Back to login</Link>
                    </div>
                </form>
            </Form>
        </AuthFormLayout>
    );
} 