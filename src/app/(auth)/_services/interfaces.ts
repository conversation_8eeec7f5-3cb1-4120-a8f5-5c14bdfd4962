import { TOrganization } from "@/app/create-organization/_services/interfaces";
import { BasicTableSchema } from "@/common/services/interfaces";

export type TRefreshTokenResponse = {
    access_token: string;
}

export type TRefreshTokenRequest = {
    refresh_token: string;
}

export enum AccountType {
    user = 'user',
    admin = 'admin',
}
  
export enum AccountRole {
    developer = 'developer', // access to only development activities and cannot perform financial transactions
    admin = 'admin', // responsible for managing an organization's system
}

export type PermissionsType = string[];
  
export interface TAccountResponse extends BasicTableSchema {
    // From accounts table
    phone_number: string;
    password: string;
    email: string;
    type: AccountType;
    role: AccountRole | null; // Since it has a default but no notNull
    permissions: PermissionsType; // Or just any[] if you don't need the specific type
    active: boolean;

  }

export type TCurrentUser = {
    account: TAccountResponse;
    organization: TOrganization;
};