import { apiRequest } from "@/common/lib/api-request";
import { REFRESH_TOKEN_KEY } from "@/common/services/constants";
import { getCookie } from "cookies-next";
import { TCurrentUser, TRefreshTokenRequest, TRefreshTokenResponse } from "./interfaces";
import { TResponse } from "@/common/services/interfaces";
import { getRoutes } from "@/common/services/constants";

export const LogoutUser = async () =>
    await apiRequest<undefined, TResponse<undefined>>({
      path: "api/v1/internal/auth/logout",
      method: "POST",
    });

export const RefreshToken = async () => {
  const refreshToken = await getCookie(REFRESH_TOKEN_KEY)

  if (!refreshToken) {
    throw new Error("No refresh token found")
  }

  return await apiRequest<TRefreshTokenRequest, TResponse<TRefreshTokenResponse>>({
    path: getRoutes.refreshToken().path,
    method: getRoutes.refreshToken().method,
    headers: {
      "Authorization": `Bearer ${refreshToken}`,
    }
  });
}

export const GetProfile = async () => {
  return await apiRequest<undefined, TResponse<TCurrentUser>>({
    path: getRoutes.getProfile().path,
    method: getRoutes.getProfile().method,
  });
}