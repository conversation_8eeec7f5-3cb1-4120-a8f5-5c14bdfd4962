"use client"
import { useMutation, UseMutationResult, UseQueryResult, useQuery } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { TResponse } from "@/common/services/interfaces";
import { GetProfile, LogoutUser, RefreshToken } from "../api";
import { TCurrentUser, TRefreshTokenResponse } from "../interfaces";


export const useLogoutMutation = () => {
    const mutation: UseMutationResult<
        TResponse<undefined>,
        AxiosError
    > = useMutation({
        mutationKey: ["logout-user"],
        mutationFn: LogoutUser,
    });

    return {mutation, data: mutation.data}
}

export const useRefreshTokenMutation = () => {
    const mutation: UseMutationResult<
        TResponse<TRefreshTokenResponse>,
        AxiosError,
        undefined
    > = useMutation({
        mutationKey: ["refresh-token"],
        mutationFn: RefreshToken,
    });

    return { mutation }
}

export const useGetProfile = () => {
    const query: UseQueryResult<
        TResponse<TCurrentUser>,
        AxiosError
    > = useQuery({
        queryKey: ["get-profile"],
        queryFn: GetProfile,
    });

    return query
}
