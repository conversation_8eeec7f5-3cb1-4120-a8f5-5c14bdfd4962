"use client"
import { useMutation, UseMutationResult } from "@tanstack/react-query";
import { TLoginRequestBody, TLoginResponse } from "../interfaces";
import { AxiosError } from "axios";
import { TResponse } from "@/common/services/interfaces";
import { LoginUser } from "../api";

export const useLoginMutation = () => {
    const mutation: UseMutationResult<
        TResponse<TLoginResponse>,
        AxiosError,
        TLoginRequestBody
    > = useMutation({
        mutationKey: ["login-user"],
        mutationFn: (data: TLoginRequestBody) => LoginUser(data),
    });

    return {mutation, data: mutation.data}
}
