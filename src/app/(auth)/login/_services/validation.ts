import { z } from "zod";

export const LoginValidateSchema = z.object({
    email: z
        .string()
        .email({message: 'Please enter a valid email address'}),
    password: z
        .string()
        .min(1, {message: 'Password is required'}),
})

export const LoginRequestBody = LoginValidateSchema.omit({
    email: true
}).extend({
    username: z
        .string()
        .min(1, {message: 'Username is required'}),
})