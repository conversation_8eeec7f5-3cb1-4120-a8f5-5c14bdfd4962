import { apiRequest } from "@/common/lib/api-request";
import { TLoginRequestBody, TLoginResponse } from "./interfaces";
import { TResponse } from "@/common/services/interfaces";
import { getRoutes } from "@/common/services/constants";

export const LoginUser = async (data: TLoginRequestBody) => 
  await apiRequest<TLoginRequestBody, TResponse<TLoginResponse>>({
    path: getRoutes.login().path,
    method: getRoutes.login().method,
    payload: data
  });
    