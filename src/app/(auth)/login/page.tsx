"use client"
import AuthFormLayout from '@/common/components/custom/auth-form-layout'
import { Form, FormControl, FormDescription, FormField, FormItem } from '@/common/components/ui/form'
import { LoginRequestBody, LoginValidateSchema } from './_services/validation';
import { useForm } from 'react-hook-form';
import { TLoginValidate } from './_services/interfaces';
import { zodResolver } from "@hookform/resolvers/zod";
import Link from 'next/link';
import ClypButton from '@/common/components/custom/clyp-button';
import AuthPageLayout from '@/common/components/custom/auth-layout';
import { useLoginMutation } from './_services/queries/queries';
import { ACCESS_TOKEN_KEY, REFRESH_TOKEN_KEY, REFRESH_TOKEN_MAX_AGE } from '@/common/services/constants';
import { PasswordInput } from '@/common/components/custom/password-input';
import PlaceholderFloatInput from '@/common/components/custom/placeholder-float-input';
import { useRouter } from 'next/navigation';
import { setCookie } from "cookies-next";
import { LOGIN_MAX_AGE } from '@/common/services/constants';
import { toastErrorMessage, toastSuccessMessage } from '@/common/lib/toast-message';

export default function LoginForm() {
    const router = useRouter()
    const {mutation} = useLoginMutation()

    const form = useForm<TLoginValidate>({
        defaultValues: {
            email: "",
            password: "",
        },
        mode: "onChange",
        resolver: zodResolver(LoginValidateSchema),
    });

    const onSubmitHandler = async(data: TLoginValidate) => {
        const payload = LoginRequestBody.parse({
            ...data, username: data.email
        })
        return mutation.mutateAsync(payload).then((response) => {
          
            setCookie(ACCESS_TOKEN_KEY, response.data.access_token, {
                maxAge: LOGIN_MAX_AGE,
                secure: true,
            });
            setCookie(REFRESH_TOKEN_KEY, response.data.refresh_token, {
                maxAge: REFRESH_TOKEN_MAX_AGE,
                secure: true,
            });
            toastSuccessMessage("Login successful")
            router.push(`/dashboard`)
        }).catch((error) => {
            
            toastErrorMessage(error.message)
        })
    }


    return (
        <AuthPageLayout>
            <AuthFormLayout 
                title='Login your account'
                description='Login and get started.'
            >
                <Form {...form}>
                    <form
                        className="space-y-5 overflow-auto h-full"
                        onSubmit={form.handleSubmit(onSubmitHandler)}
                    >
                        <section className="space-y-1">
                            <FormField
                                control={form.control}
                                name="email"
                                disabled={mutation.isLoading}
                                render={({ field }) => (
                                    <FormItem>
                                        <FormControl>
                                            <PlaceholderFloatInput
                                                placeholder='Email Address'
                                                field={field}
                                                name='email'
                                            />
                                        </FormControl>
                                        <FormDescription className='form-description'>
                                            Must be a valid email address
                                        </FormDescription>
                                    </FormItem>
                                )}
                            />
                        </section>
                        <section className='space-y-1'>
                            <FormField
                                control={form.control}
                                name="password"
                                disabled={mutation.isLoading}
                                render={({ field }) => (
                                    <FormItem>
                                        <FormControl>
                                            <PasswordInput
                                                {...field}
                                                className="form-input-text focus:outline-none! px-5 h-[64px] rounded-full"
                                                placeholder="Password"
                                            />
                                        </FormControl>
                                    </FormItem>
                                )}
                            />
                            <FormDescription className='form-description'>
                                Must be have upper case, lower case , number and symbol.
                            </FormDescription>
                        </section>

                        <div className="flex justify-end">
                            <Link href="/forgot-password" className="form-redirect-link">Forgot Password?</Link>
                        </div>

                        <ClypButton 
                            classes='text-white font-bold w-full h-[60px] rounded-full'
                            text="Log In"
                            type="submit"
                            disabled={mutation.isLoading || !form.formState.isValid}
                            isLoading={mutation.isLoading}
                        />

                        <div className="form-redirect-text">
                            {"Don't have an account? "}
                            <Link href="/signup" className="link">Signup</Link>
                        </div>
                    </form>
                </Form>
            </AuthFormLayout>
        </AuthPageLayout>
    )
}
