import * as React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/common/components/ui/select";
import { phone_prefixes } from "../_services/utilities";
import { ControllerRenderProps } from "react-hook-form";
import { TSignupValidate } from "../_services/interfaces";

export function PhonePrefixSelect({
  field,
}: {
  field: ControllerRenderProps<TSignupValidate, "phone_prefix"> | null;
}) {
  if(!field) return null
  
  return (
    <Select value={field.value} onValueChange={field.onChange}>
      <SelectTrigger 
        className="w-[80px] border-none shadow-none h-[40px] bg-orange text-white rounded-full"
    >
        <SelectValue placeholder="Select prefix" />
      </SelectTrigger>
      <SelectContent>
        {phone_prefixes.map((prefix, index) => (
          <SelectItem value={prefix} key={index}>
            {prefix}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}