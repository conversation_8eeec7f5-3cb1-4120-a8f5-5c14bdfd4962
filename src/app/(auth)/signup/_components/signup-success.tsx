/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react'
import Image from 'next/image';
import ClypButton from '@/common/components/custom/clyp-button';
import Link from 'next/link';
// import { string } from 'zod';

interface sucessProperties {
    title: string,
    description: string,
    route?: any,
    btnText: string,
}


export default function SuccessModal({title, description, route, btnText}: sucessProperties) {

    return (
        <section className='pt-10 flex flex-col items-center gap-7'>
            <Image
                width={212}
                height={212}
                alt="success-icon"
                src="/icons/check-fill.svg"
            />
            <div className="space-y-1 text-center px-7">
                <h1 className='text-bold-32'>{title}</h1>
                <p className='text-body-2 text-[#575A65]'>{description}</p>
            </div>
            <Link href={route} className='w-full'>
                <ClypButton
                    classes="w-full rounded-full h-[60px] text-white"
                    text={btnText}
                />
            </Link>
        </section>
    )
}
