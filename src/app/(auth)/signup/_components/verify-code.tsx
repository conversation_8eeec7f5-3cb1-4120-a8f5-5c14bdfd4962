"use client";
import AuthFormLayout from '@/common/components/custom/auth-form-layout'
import { Form, FormControl, FormDescription, FormField, FormItem } from '@/common/components/ui/form'
import React from 'react'
import { VerifyCodeSchema } from '../_services/validation';
import { useForm } from 'react-hook-form';
import { TVerifyCode } from '../_services/interfaces';
import { zodResolver } from "@hookform/resolvers/zod";
import Link from 'next/link';
import { Button } from '@/common/components/ui/button';
import {
    InputOTP,
    InputOTPGroup,
    InputOTPSlot,
} from "@/common/components/ui/input-otp"
import { REGEXP_ONLY_DIGITS } from "input-otp"
import '../_styles/otp-style.css';
  

export default function VerifyCode() {

    const form = useForm<TVerifyCode>({
        defaultValues: {
            code: ""
        },
        mode: "onChange",
        resolver: zod<PERSON>esolver(VerifyCodeSchema),
    });


    const onSubmitHandler = (data: TVerifyCode) => {
        console.log(data)
    }

    return (
        <div className='pt-10'>
            <AuthFormLayout 
                title='Input Code'
                description='Input the code sent to your email address.'
            >
                <Form {...form}>
                    <form
                        className="space-y-5 h-full overflow-hidden"
                        onSubmit={form.handleSubmit(onSubmitHandler)}
                    >
                        <FormField
                            control={form.control}
                            name="code"
                            render={({ field }) => (
                                <FormItem>
                                    <FormControl>
                                        <InputOTP maxLength={6} pattern={REGEXP_ONLY_DIGITS} {...field}>
                                            <InputOTPGroup className="cp-otp-group">
                                                <InputOTPSlot index={0} />
                                                <InputOTPSlot index={1} />
                                                <InputOTPSlot index={2} />
                                                <InputOTPSlot index={3} />
                                            </InputOTPGroup>
                                        </InputOTP>
                                    </FormControl>
                                </FormItem>
                            )}
                        />
                        

                        <div className="">
                            <FormDescription>
                                {"Didn't get the code? "}
                                <Link 
                                    href="#" 
                                    className="text-green-600 font-bold ml-3"
                                >Resend code</Link>
                            </FormDescription>
                        </div>

                        <Button className='bg-green-600 hover:bg-initial text-white font-bold w-full h-[60px] rounded-full'>Proceed</Button>
                    </form>
                </Form>
            </AuthFormLayout>
        </div>
    )
}
