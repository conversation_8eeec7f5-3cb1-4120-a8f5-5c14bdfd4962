"use client";
import AuthFormLayout from '@/common/components/custom/auth-form-layout'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel } from '@/common/components/ui/form'
import { MobileSignupValidate, SignupRequestBody, SignupValidateSchema } from '../_services/validation';
import { useForm } from 'react-hook-form';
import { PhonePrefix, TMobileSignupValidate, TSignupValidate } from '../_services/interfaces';
import { zodResolver } from "@hookform/resolvers/zod";
import { PhonePrefixSelect } from './phone-prefix-select';
import { Separator } from '@radix-ui/react-select';
import Link from 'next/link';
import { useSignupMutation } from '../_services/queries/queries';
import ClypButton from '@/common/components/custom/clyp-button';
import { useRouter } from 'next/navigation';
import PlaceholderFloatInput from '@/common/components/custom/placeholder-float-input';
import { PasswordInput } from '@/common/components/custom/password-input';
import { DEFAULT_VALUES, MOBILE_DEFAULT_VALUES } from '../_services/constants';
import { useIsMobile } from '@/common/hooks/use-mobile';
import { toastErrorMessage, toastSuccessMessage } from '@/common/lib/toast-message';

export default function SignupForm() {
    const {mutation} = useSignupMutation();
    const isMobile = useIsMobile()

    const router = useRouter()

    const form = useForm<TMobileSignupValidate | TSignupValidate>({
        defaultValues: isMobile? MOBILE_DEFAULT_VALUES : DEFAULT_VALUES,
        mode: "onChange",
        resolver: zodResolver(isMobile? MobileSignupValidate : SignupValidateSchema),
    });

    const onSubmitHandler = async (data: TMobileSignupValidate | TSignupValidate) => {
        return mutation.mutateAsync(SignupRequestBody.parse({
            ...data,
            phone_number: data.phone_prefix ? `${data.phone_prefix}${data.phone_number}` : data.phone_number
        })).then(() => {
            toastSuccessMessage("Registration successful, please proceed to login")
            router.push('/login')
        }).catch((error) => {
            toastErrorMessage(error.message)
        })
    }

    return (
        <div>
            <AuthFormLayout 
                title='Welcome to Payment Business'
                description='Sign Up and get started.'
            >
                <Form {...form}>
                    <form
                        className="space-y-5 overflow-auto h-full"
                        onSubmit={form.handleSubmit(onSubmitHandler)}
                    >
                        <section className='space-y-1'>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <FormField
                                    control={form.control}
                                    name="first_name"
                                    disabled={mutation.isLoading}
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormControl>
                                            <PlaceholderFloatInput 
                                                field={field}  
                                                name="first_name"
                                                placeholder="First Name"
                                            />
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="last_name"
                                    disabled={mutation.isLoading}
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormControl>
                                            <PlaceholderFloatInput 
                                                field={field}  
                                                name="last_name"
                                                placeholder="Last Name"
                                            />
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />
                            </div>
                            <FormDescription className='text-medium-10 md:text-medium-14'>
                                Please ensure this is the name on your Government ID document.
                            </FormDescription>
                        </section>
                        <section className="space-y-1">
                            <FormField
                                control={form.control}
                                name="email"
                                disabled={mutation.isLoading}
                                render={({ field }) => (
                                    <FormItem>
                                        <FormControl>
                                            <PlaceholderFloatInput 
                                                field={field}  
                                                name="email"
                                                placeholder='Email Address'
                                            />
                                        </FormControl>
                                        <FormDescription className='text-medium-10 md:text-medium-14'>
                                            Must be a valid email address
                                        </FormDescription>
                                    </FormItem>
                                )}
                            />
                        </section>
                        <section className="">
                            {!isMobile && <FormLabel className='text-xs font-light'>Phone Number</FormLabel>}
                            <div className='flex items-center gap-2 border rounded-full h-[52px] md:h-[64px] px-2 md:px-5 overflow-hidden'>
                                {!isMobile && <>
                                    <div className='flex-none w-[60px] z-10'>
                                        <FormField
                                            control={form.control}
                                            name="phone_prefix"
                                            disabled={mutation.isLoading}
                                            render={({ field }) => (
                                                <PhonePrefixSelect field={field && field.value !== null ? { ...field, value: field.value as PhonePrefix } : null} />
                                            )}
                                        />
                                    </div>
                                    <Separator dir='tb' />
                                </>
                                }
                                <div className='grow'>
                                        <FormField
                                            control={form.control}
                                            name="phone_number"
                                            disabled={mutation.isLoading}
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormControl>
                                                        <PlaceholderFloatInput 
                                                            field={field}  
                                                            name="phone_number"
                                                            placeholder={isMobile? "Phone number" : "9000000000"}
                                                            hidePlaceholderOnFocus
                                                            hideBorder
                                                            hidePlaceholderOnType={isMobile}
                                                        />
                                                    </FormControl>
                                                </FormItem>
                                            )}
                                        />
                                </div>
                            </div>
                            <FormDescription className='text-medium-10 md:text-medium-14'>
                                Must be a valid phone number
                            </FormDescription>
                        </section>
                        <section className='space-y-1'>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-y-7 md:gap-2">
                                <FormField
                                    control={form.control}
                                    name="password"
                                    disabled={mutation.isLoading}
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormControl>
                                                <PasswordInput
                                                    {...field}
                                                    placeholder="Password"
                                                    className="text-regular-16  focus:outline-none! h-[52px] md:h-[64px]"
                                                />
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />
                                <FormField
                                    control={form.control}
                                    name="confirm_password"
                                    disabled={mutation.isLoading}
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormControl>
                                                <PasswordInput
                                                    {...field}
                                                    placeholder="Re-enter Password"
                                                    className="text-regular-16  focus:outline-none! h-[52px] md:h-[64px]"
                                                />
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />
                            </div>
                            <FormDescription className='text-medium-10 md:text-medium-14'>
                            Must contain at least an uppercase letter, a lowercase letter, a number and a special character
                            </FormDescription>
                        </section>

                        <ClypButton
                            classes='w-full h-[55px] rounded-full'
                            text="Create Account"
                            type="submit"
                            disabled={mutation.isLoading || !form.formState.isValid}
                            isLoading={mutation.isLoading}
                        />

                        <div className="form-redirect-text">
                            {"Already have an account? "}
                            <Link href="/login" className="link">Login</Link>
                        </div>
                    </form>
                </Form>
            </AuthFormLayout>
        </div>
    )
}
