import AuthPageLayout from '@/common/components/custom/auth-layout'
import React from 'react'
import SignupForm from './_components/signup-form';
import { IndexPageProps } from '@/common/services/interfaces';
import VerifyCode from './_components/verify-code';
import SuccessModal from './_components/signup-success';
import { SignupProgress } from './_services/interfaces';

export default async function SignupPage({ searchParams }: IndexPageProps) {
    const search = await searchParams
    const progress = search.progress as SignupProgress

    const progressComponents = {
        [SignupProgress.VERIFY_EMAIL]: <VerifyCode />,
        [SignupProgress.SUCCESS]: <SuccessModal
            title="Create Account Successful!"
            description='Hello Andz, you have successfully created your account. Click proceed to login.'
            route={"/login"}
            btnText='Proceed to login'
        />,
    };

    return (
        <AuthPageLayout>
            {progressComponents[progress] || <SignupForm />}
        </AuthPageLayout>
    )

}
