import { useMutation, UseMutationResult } from "@tanstack/react-query";
import {  TAuthUser, TSignupRequestBody } from "../interfaces";
import { AxiosError } from "axios";
import { RegisterUser } from "../api";
import { TResponse } from "@/common/services/interfaces";

export const useSignupMutation = () => {
    
    const mutation: UseMutationResult<
        TResponse<TAuthUser>,
        AxiosError,
        TSignupRequestBody
    > = useMutation({
        mutationKey: ["register-user"],
        mutationFn: RegisterUser,
    });

    return {mutation, data: mutation.data}
}
