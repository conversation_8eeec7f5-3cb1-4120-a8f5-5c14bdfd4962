import { z } from "zod";
import { MobileSignupValidate, SignupRequestBody, SignupValidateSchema, VerifyCodeSchema } from "./validation";
import { BasicTableSchema } from "@/common/services/interfaces";

export type TSignupRequestBody = z.infer<typeof SignupRequestBody>
export type TSignupValidate = z.infer<typeof SignupValidateSchema>
export type TMobileSignupValidate = z.infer<typeof MobileSignupValidate>
export type TVerifyCode = z.infer<typeof VerifyCodeSchema>

export enum SignupProgress {
    VERIFY_EMAIL = 'code_verifier',
    SUCCESS = 'success'
}

export enum UserType {
    user = 'user',
}
  
export enum UserRole {
    developer = 'developer',
    admin = 'admin',
}

  
export interface TAuthUser extends BasicTableSchema {
    first_name: string;
    last_name: string;
    middle_name?: string;
    email: string;
    phone_number: string;
    type: UserType;
    role: UserRole;
    permissions: string[];
    active: boolean;
}

export enum PhonePrefix {
    NIGERIA = '+234', // Nigeria
    KENYA = '+254', // Kenya
    UGANDA = '+256', // Uganda
    SOUTH_AFRICA = '+27', // South Africa
    GHANA = '+233' // Ghana
}