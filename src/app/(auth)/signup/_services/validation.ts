import { z } from "zod";
import { PhonePrefix } from "./interfaces";

const baseSchema = z.object({
    first_name: z
        .string()
        .min(1, {message: 'First name is required'}),
    last_name: z
        .string()
        .min(1, {message: 'Last name is required'}),
    email: z
        .string()
        .email({message: 'Please enter a valid email address'}),
    phone_number: z
        .string()
        .min(1, {message: 'Phone number is required'})
        .transform((value) => {
            return value.startsWith("0") ? value.slice(1) : value;
        }),
    password: z
        .string()
        .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\w\s]).{8,}$/, {
          message: 'Password must contain at least an uppercase letter, a lowercase letter, a number and a special character',
        }),
})

export const SignupRequestBody = baseSchema.extend({
    middle_name: z
        .string()
        .optional(),
})

const SignupSchema = baseSchema.extend({
    confirm_password: z
        .string()
        .min(1, { message: 'Please re-enter your password' }),
})

export const MobileSignupValidate = SignupSchema.extend({
    phone_prefix: z
        .nativeEnum(PhonePrefix)
        .nullable()
}).refine(
    (data) => data.password === data.confirm_password,
    {
        message: 'Passwords do not match',
        path: ['confirm_password'],
    }
);

export const SignupValidateSchema = SignupSchema.extend({
    phone_prefix: z
        .nativeEnum(PhonePrefix, {message: 'Please select a phone prefix'})
})
.refine(
    (data) => data.password === data.confirm_password,
    {
        message: 'Passwords do not match',
        path: ['confirm_password'],
    }
);

export const VerifyCodeSchema = z.object({
    code: z
        .string({ message: "Verification code is required" })
})