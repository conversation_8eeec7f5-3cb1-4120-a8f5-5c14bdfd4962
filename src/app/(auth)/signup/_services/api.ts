import { apiRequest } from "@/common/lib/api-request";
import { TAuthUser, TSignupRequestBody } from "./interfaces";
import { TResponse } from "@/common/services/interfaces";
import { getRoutes } from "@/common/services/constants";


export const RegisterUser = async (data: TSignupRequestBody) =>
    await apiRequest<TSignupRequestBody, TResponse<TAuthUser>>({
      path: getRoutes.register().path,
      method: getRoutes.register().method,
      payload: data
    });