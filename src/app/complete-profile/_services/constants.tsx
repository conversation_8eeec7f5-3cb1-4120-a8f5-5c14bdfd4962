import { GENERAL_REGULATORY_DOCUMENTS } from "@/app/create-organization/_services/interfaces"
import { TOrganizationDocument } from "./interfaces"

// Shortens the long Enum
export const DocumentHash = {
    REGISTRATION_CERTIFICATE: GENERAL_REGULATORY_DOCUMENTS.BUSINESS_REGISTRATION_CERTIFICATE,
    ARTICLES_OF_INCORPORATION: GENERAL_REGULATORY_DOCUMENTS.BUSINESS_ARTICLES_OF_INCORPORATION,
    DIRECTOR_ID_DOC: GENERAL_REGULATORY_DOCUMENTS.DIRECTOR_ID_DOC
}

export const ORGANIZATION_DOCUMENTS: TOrganizationDocument[] = [
    {
        document: DocumentHash.REGISTRATION_CERTIFICATE,
        title: "Business Registration Certificate"
    },
    {
        document: DocumentHash.ARTICLES_OF_INCORPORATION,
        title: "Business Articles of Incorporation"   
    },
    {
        document: DocumentHash.DIRECTOR_ID_DOC,
        title: "Director ID Documents"
    }
]

export const DEFAULT_DOCS = {
    [DocumentHash.REGISTRATION_CERTIFICATE]: [],
    [DocumentHash.ARTICLES_OF_INCORPORATION]: [],
    [DocumentHash.DIRECTOR_ID_DOC]: [],
}
 