import { apiRequest } from "@/common/lib/api-request";
import { TResponse } from "@/common/services/interfaces";
import { TFileResponse, TSubmitCompliance } from "./interfaces";
import { TOrganization } from "@/app/create-organization/_services/interfaces";
import { getRoutes } from "@/common/services/constants";


export const UploadComplianceDoc = async (data: FormData) =>
    await apiRequest<FormData, TResponse<TFileResponse>>({
      path: getRoutes.uploadComplianceDoc().path,
      method: getRoutes.uploadComplianceDoc().method,
      payload: data,
      headers: { 'Content-Type': 'multipart/form-data' },
    });

export const SubmitComplianceDocs = async (data: TSubmitCompliance) =>
    await apiRequest<TSubmitCompliance, TResponse<TOrganization>>({
      path: getRoutes.submitComplianceDocs().path,
      method: getRoutes.submitComplianceDocs().method,
      payload: data,
    });

export const RemoveUploadedDoc = async (fileId: string) =>
    await apiRequest<string, TResponse<undefined>>({
      path: getRoutes.removeUploadedDoc(fileId).path,
      method: getRoutes.removeUploadedDoc(fileId).method,
    });
