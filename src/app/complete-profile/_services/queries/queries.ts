"use client"
import { useMutation, UseMutationResult } from "@tanstack/react-query";
import { TFileResponse, TSubmitCompliance } from "../interfaces";
import { AxiosError } from "axios";
import { TResponse } from "@/common/services/interfaces";
import { RemoveUploadedDoc, SubmitComplianceDocs, UploadComplianceDoc } from "../api";
import { TOrganization } from "@/app/create-organization/_services/interfaces";


export const useUploadComplianceDoc = () => {
    const mutation: UseMutationResult<
        TResponse<TFileResponse>,
        AxiosError,
        FormData
    > = useMutation({
        mutationKey: ["upload-compliance-doc"],
        mutationFn: UploadComplianceDoc,
    });

    return {mutation, response: mutation.data}
}

export const useSubmitComplianceDocs = () => {
    const mutation: UseMutationResult<
        TResponse<TOrganization>,
        AxiosError,
        TSubmitCompliance
    > = useMutation({
        mutationKey: ["submit-compliance-docs"],
        mutationFn: SubmitComplianceDocs,
    });

    return {mutation, response: mutation.data}
}

export const useRemoveUploadedDoc = () => {
    const mutation: UseMutationResult<
        TResponse<undefined>,
        AxiosError,
        string
    > = useMutation({
        mutationKey: ["remove-uploaded-doc"],
        mutationFn: RemoveUploadedDoc,
    });

    return {mutation, response: mutation.data}
}