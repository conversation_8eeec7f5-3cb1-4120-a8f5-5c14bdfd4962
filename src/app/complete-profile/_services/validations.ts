import { GENERAL_REGULATORY_DOCUMENTS, INDUSTRY } from "@/app/create-organization/_services/interfaces";
import { 
    FILE_RESPONSE_SCHEMA, 
    REGULATORY_COMPLIANCE_DOCUMENTS_SCHEMA 
} from "@/app/create-organization/_services/validations";
import { z } from "zod";

export const UpdateBusinessInfoSchema = z.object({
    business_name: z
        .string()
        .min(1, {message: 'Business name is required'}),
    business_email: z
        .string()
        .email({message: 'Please enter a valid email address'}),
    business_type: z
        .string()
        .email({message: 'Business type is required'}),
    industry: z
        .string()
        .min(1, {message: 'This field is required'}),
})

export const UpdateBusinessInfoWithCacSchema = UpdateBusinessInfoSchema.extend({
    reg_no: z
        .string()
        .min(1, {message: 'This field is required.'}),
    tin: z
        .string()
        .min(1, {message: 'This field is required.'})
})

export const CreateOrganizationComplianceSchema = z.object({
    name: z.string().min(1, {message: 'Organization name is required.'}),
    industry: z.nativeEnum(INDUSTRY, {message: 'Please select an industry.'}),
    regulatory_compliance_docs: REGULATORY_COMPLIANCE_DOCUMENTS_SCHEMA,
})

export const UploadSchema = z.object({
    file: z
        .instanceof(File)
        .refine((file) => ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(file.type), {
            message: "File must be a PDF or Word document",
        }),
});

export const SubmitComplianceSchema = z.object({
    industry: z.nativeEnum(INDUSTRY),
    regulatory_compliance_docs: REGULATORY_COMPLIANCE_DOCUMENTS_SCHEMA
})


export const ComplianceDocumentsSchema = z.record(
    z.nativeEnum(GENERAL_REGULATORY_DOCUMENTS),
    z.array(FILE_RESPONSE_SCHEMA).nonempty(),
)
