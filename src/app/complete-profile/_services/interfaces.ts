import { z } from "zod";
import { 
    ComplianceDocumentsSchema,
    SubmitComplianceSchema, 
    UpdateBusinessInfoSchema, 
    UpdateBusinessInfoWithCacSchema, 
    UploadSchema 
} from "./validations";
import { GENERAL_REGULATORY_DOCUMENTS } from "@/app/create-organization/_services/interfaces";
import { FILE_RESPONSE_SCHEMA } from "@/app/create-organization/_services/validations";

export type TFileResponse = z.infer<typeof FILE_RESPONSE_SCHEMA>

export type TUploadData = z.infer<typeof UploadSchema>  


export type TUpdateBusinessInfoWithCac = z.infer<typeof UpdateBusinessInfoWithCacSchema>
export type TUpdateBusinessInfo = z.infer<typeof UpdateBusinessInfoSchema>

export type UpdatBusinessInfoUnion = TUpdateBusinessInfoWithCac | TUpdateBusinessInfo

export type TSubmitCompliance = z.infer<typeof SubmitComplianceSchema>

export type TOrganizationDocument = {
    title: string,
    document: GENERAL_REGULATORY_DOCUMENTS
}

export type TComplianceDocuments = z.infer<typeof ComplianceDocumentsSchema>