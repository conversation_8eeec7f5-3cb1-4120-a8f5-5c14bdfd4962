"use client"
import { Icons } from '@/common/components/custom/icon-templates'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/common/components/ui/card'
import React, { useEffect, useState } from 'react'
import { TComplianceDocuments, TFileResponse, TSubmitCompliance } from './_services/interfaces'
import { useRouter } from 'next/navigation'
import useOrganizationStore from '@/common/store/use-organization-store'
import { GENERAL_REGULATORY_DOCUMENTS, INDUSTRY } from '../create-organization/_services/interfaces'
import { SubmitComplianceSchema } from './_services/validations'
import ProfileHeader from './_components/profile-header'
import UploadDocsStage from './_components/upload-docs-stage'
import { DEFAULT_DOCS, DocumentHash } from './_services/constants'
import ClypButton from '@/common/components/custom/clyp-button'

import { StageFormContext } from './_contexts/stage-form-context'
import { useSubmitComplianceDocs } from './_services/queries/queries'
import { toastErrorMessage, toastSuccessMessage } from '@/common/lib/toast-message'
import { INDUSTRY_MAP } from '../create-organization/_services/constants'
import { Select, SelectValue, SelectTrigger, SelectContent, SelectItem } from '@/common/components/ui/select'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Form, FormField, FormItem, FormControl } from '@/common/components/ui/form'


import './_styles/profile.css'
import { REGULATORY_COMPLIANCE_DOCUMENTS_SCHEMA } from '../create-organization/_services/validations'

export default function CompleteProfileLayout() {
    const router = useRouter()
    const { mutation } = useSubmitComplianceDocs()
    const { organization, setOrganization } = useOrganizationStore()

    const [industry, setIndustry] = useState(INDUSTRY.ECOMMERCE)
    const [documents, setDocuments] = useState<TComplianceDocuments>(DEFAULT_DOCS);

    const form = useForm<TSubmitCompliance>({
        defaultValues: {
            regulatory_compliance_docs: {
                [DocumentHash.REGISTRATION_CERTIFICATE]: [],
                [DocumentHash.ARTICLES_OF_INCORPORATION]: [],
                [DocumentHash.DIRECTOR_ID_DOC]: []
            }
        },
        resolver: zodResolver(SubmitComplianceSchema)
    })

    useEffect(() => {
        if (organization) {
            setIndustry(organization.industry)
            form.setValue('industry', organization.industry)
        }

        // eslint-disable-next-line
    }, [organization])


    const removeFile = (document: GENERAL_REGULATORY_DOCUMENTS, fileId: string) => {

        const compliance_docs = form.getValues('regulatory_compliance_docs');

        const target_doc = compliance_docs[document] as string[];

        const filtered_files = target_doc.filter(file => file !== fileId)

        form.setValue('regulatory_compliance_docs', {
            ...form.getValues('regulatory_compliance_docs'),
            [document]: filtered_files
        })

        setDocuments((prev) => ({
            ...prev,
            [document]: (prev[document] || []).filter((file) => file.id !== fileId)
        }))
    }

    const fileUploaded = (document: GENERAL_REGULATORY_DOCUMENTS, file: TFileResponse) => {

        const current_files = documents[document] || []
        const updated_files = [...current_files, file]

        setDocuments((prev) => ({
            ...prev,
            [document]: updated_files
        }))

        form.setValue('regulatory_compliance_docs', {
            ...form.getValues('regulatory_compliance_docs'),
            [document]: [...(form.getValues('regulatory_compliance_docs')[document] || []), file.id]
        })
    }


    const submitCompliance = (data: TSubmitCompliance) => {
        mutation.mutateAsync(data).then((response) => {
            setOrganization(response.data)
            toastSuccessMessage(response.message)
            router.push('/dashboard')
        }).catch((error) => {
            toastErrorMessage(error.message ?? 'An error occured, please try again')
        })
    }

    const industryValue = form.watch('industry')
    const docValues = form.watch('regulatory_compliance_docs')

    const formIsValid = REGULATORY_COMPLIANCE_DOCUMENTS_SCHEMA.safeParse(docValues).success && industryValue


    return (
        <StageFormContext.Provider value={{
            industry,
            fileUploaded,
            removeFile,
            documents
        }}>
            <Card className='relative shadow-none rounded-none border-none min-h-screen'>

                <CardHeader className='flex-row items-center gap-x-5 border-b'>
                    <Icons.closecircle
                        className='text-lg'
                        role='button'
                        onClick={() => router.back()}
                    />
                    <CardTitle className='text-gray-2 text-semibold-24'>Complete your account setup</CardTitle>
                </CardHeader>

                <CardContent>
                    <div className="space-y-6 pt-20 max-w-[680px] relative">

                        <ProfileHeader />

                        <Form {...form}>
                            <form
                                className="space-y-7 overflow-auto h-full"
                                onSubmit={form.handleSubmit(submitCompliance)}
                            >

                                <FormField
                                    control={form.control}
                                    name="industry"
                                    disabled={mutation.isLoading}
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormControl>
                                                <Select
                                                    onValueChange={field.onChange}
                                                    value={field.value}
                                                    disabled={mutation.isLoading}
                                                >
                                                    <SelectTrigger className="rounded-full w-full shadow-none h-[60px] text-medium-18">
                                                        <SelectValue placeholder="Industry" />
                                                    </SelectTrigger>
                                                    <SelectContent className='rounded-2xl'>
                                                        {Object.keys(INDUSTRY_MAP).map((key, index) => (
                                                            <SelectItem
                                                                value={key}
                                                                key={index}
                                                                className="capitalize rounded-full p-3"
                                                            >
                                                                {INDUSTRY_MAP[key as keyof typeof INDUSTRY_MAP]}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </FormControl>
                                        </FormItem>
                                    )}
                                />

                                <UploadDocsStage
                                    disabled={mutation.isLoading}
                                />

                                <ClypButton
                                    text='Submit'
                                    classes="h-[60px] text-medium-18 text-white w-[60%]"
                                    disabled={mutation.isLoading || !formIsValid}
                                    isLoading={mutation.isLoading}
                                    type='submit'
                                />

                            </form>
                        </Form>
                    </div>
                </CardContent>
            </Card>
        </StageFormContext.Provider>
    )
}
