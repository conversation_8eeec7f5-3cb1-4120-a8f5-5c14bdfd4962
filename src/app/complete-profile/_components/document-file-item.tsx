"use client"
import { Card, CardContent } from '@/common/components/ui/card'
import { useContext } from 'react'
import { TFileResponse } from '../_services/interfaces'
import { Icons } from '@/common/components/custom/icon-templates'
import { GENERAL_REGULATORY_DOCUMENTS } from '@/app/create-organization/_services/interfaces'
import { StageFormContext } from '../_contexts/stage-form-context'
import { useRemoveUploadedDoc } from '../_services/queries/queries'
export default function DocumentFileItem({
    file,
    document,
}: {file: TFileResponse, index?: number, document: GENERAL_REGULATORY_DOCUMENTS}) {
    const { removeFile } = useContext(StageFormContext);
    const {mutation} = useRemoveUploadedDoc()

    const removeDocumentFile = () => {
        mutation.mutateAsync(String(file.id))
        removeFile(document, file.id)
    }

    return (
        <Card className='mb-3 shadow-none'>
            <CardContent className='flex items-center p-2 gap-2'>
                <div className='flex-none w-[70px] h-[70px] bg-gray-8'></div>
                <div className='grow text-medium-16 text-gray-2'>{file.fileName}</div>
                <Icons.trash 
                    className='flex-none w-8 hover:text-red-500' 
                    role='button' onClick={removeDocumentFile} 
                /> 
            </CardContent>
        </Card>
    )
}
