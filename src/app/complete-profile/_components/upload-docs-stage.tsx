
import { ORGANIZATION_DOCUMENTS } from "../_services/constants";
import UploadDocFiles from "./upload-doc-files";
import { Accordion } from "@/common/components/ui/accordion"
  
export default function UploadDocsStage({disabled}: {disabled?: boolean}) {
    return (
        <div className="max-w-[700px]">
            <Accordion type="single" collapsible>
                {ORGANIZATION_DOCUMENTS.map((document, index) => (
                    <UploadDocFiles 
                        index={index} 
                        key={index} 
                        document={document} 
                        disabled={disabled}
                    />
                ))}
            </Accordion>
        </div>
    )
}