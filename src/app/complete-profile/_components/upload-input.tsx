"use client"
import { Icons } from '@/common/components/custom/icon-templates'
import { Card, CardContent } from '@/common/components/ui/card'
import { useContext } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Input } from '@/common/components/ui/input'
import { UploadSchema } from '../_services/validations'
import { Form, FormControl, FormField, FormItem } from '@/common/components/ui/form'
import { useUploadComplianceDoc } from '../_services/queries/queries'
import { TUploadData } from '../_services/interfaces'
import { toastErrorMessage, toastSuccessMessage } from '@/common/lib/toast-message'
import { GENERAL_REGULATORY_DOCUMENTS } from '@/app/create-organization/_services/interfaces'
import { Loader } from 'lucide-react'
import ClypButton from '@/common/components/custom/clyp-button'
import { StageFormContext } from '../_contexts/stage-form-context'

interface UploadInputProps {
    document: GENERAL_REGULATORY_DOCUMENTS
    disabled?: boolean,
}

export default function UploadInput({document: doc, disabled}: UploadInputProps) {

    const { fileUploaded } = useContext(StageFormContext)
    const {mutation} = useUploadComplianceDoc()

    const form = useForm<TUploadData>({
        defaultValues: { },
        resolver: zodResolver(UploadSchema),
    });

    const onSubmitHandler = async(data: TUploadData) => {
        const formData = new FormData();
        formData.append('file', data.file)

        return mutation.mutateAsync(formData).then((response) => {
           
            if(!response.data) {
                toastErrorMessage("Upload failed, please try again.")
                return
            }
            fileUploaded(doc, response.data)
            toastSuccessMessage(response.message)
        }).catch(() => {
            toastErrorMessage("Upload failed, please try again.")
        })
    }
    
    return (
        <section className='space-y-7'>
            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmitHandler)}>
                    <Card className='w-full shadow-none border-dashed border-gray-6 mx-auto'>
                        <CardContent 
                            className='p-3 h-[60px] flex items-center justify-center text-center'
                            role='button' onClick={() => document.getElementById('file-upload')?.click()}
                        >
                            {mutation.isLoading? (
                                <p className="text-gray-5 text-regular-16">
                                    Please wait <Loader className='animate-spin inline' />
                                </p>
                            ): (
                                <p className="text-gray-5 text-regular-16">
                                    <Icons.cloudUpload className='inline' /> Click to upload document
                                </p>
                            )}
                            <FormField
                                control={form.control}
                                name="file"
                                disabled={mutation.isLoading || disabled}
                                render={({field}) => (
                                    <FormItem>
                                        <FormControl>
                                            <Input
                                                type="file"
                                                accept=".docx,.doc,.pdf"
                                                onChange={(e) => {
                                                    const selectedFile = e.target.files?.[0];
                                                    if(selectedFile) {
                                                        field.onChange(selectedFile)
                                                        onSubmitHandler({file: selectedFile})
                                                    }
                                                }}
                                                className="hidden"
                                                id="file-upload"
                                                disabled={mutation.isLoading || disabled}
                                            />
                                        </FormControl>
                                    </FormItem>
                                )}
                            />
                        </CardContent>
                    </Card>
                    <ClypButton text="Go" id="go" type="submit" className='hidden' />
                </form>
            </Form>
        </section>
    )
}