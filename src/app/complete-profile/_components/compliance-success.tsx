import React from 'react'
import Cly<PERSON>Button from '@/common/components/custom/clyp-button';
import { Dialog, DialogContent } from '@/common/components/ui/dialog';
import { Icons } from '@/common/components/custom/icon-templates';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';

interface ComplianceSuccessProps {
  isOpen: boolean;
  onOpenChange: (value: boolean) => void;
}

export default function ComplianceSuccess({ isOpen, onOpenChange }: ComplianceSuccessProps) {
  const router = useRouter();
  const queryClient = useQueryClient();

  const handleGoToDashboard = () => {
    // Invalidate the profile query to force a refetch when landing on dashboard
    queryClient.invalidateQueries(["get-profile"]);
    // Navigate to dashboard
    router.push('/dashboard');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent 
        className="max-w-sm px-6"
        onEscapeKeyDown={(e) => e.preventDefault()}
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        <section className='pt-10 flex flex-col items-center gap-7'>
          <Icons.success className='size-28' />
          <div className="space-y-1 text-center px-3">
            <h1 className='text-bold-32'>Documents Submitted</h1>
            <p className='text-body-2 text-[#575A65]'>
              Your business documents have been successfully submitted for review. 
              We&apos;ll notify you once the review is complete.
            </p>
          </div>
          <ClypButton 
            classes="w-[225px] rounded-md h-[56px] text-white"
            text="Go to Dashboard"
            onClick={handleGoToDashboard}
          />
        </section>
      </DialogContent>
    </Dialog>
  )
} 