"use client"
import { useContext } from 'react'
import { TOrganizationDocument } from '../_services/interfaces'
import {
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/common/components/ui/accordion"
import DocumentFileItem from './document-file-item'
import UploadInput from './upload-input'
import { StageFormContext } from '../_contexts/stage-form-context'

interface UploadDocFilesProps {
    document: TOrganizationDocument,
    index: number,
    disabled?: boolean
}

export default function UploadDocFiles({
    document, 
    index, 
    disabled
}: UploadDocFilesProps) {
    const document_type = document.document
    const {documents} = useContext(StageFormContext)

    const docs = documents[document_type]

    return (
        <AccordionItem value={String(index)}>
            <AccordionTrigger>
                <span className='text-gray-5 text-medium-16'>{document.title}</span>
            </AccordionTrigger>
            <AccordionContent>
                {docs?.map((doc, index) => (
                    <DocumentFileItem 
                        key={index}
                        document={document_type}
                        file={doc}
                        index={index}
                    />
                ))}
                <UploadInput 
                    document={document_type} 
                    disabled={disabled}
                />
            </AccordionContent>
        </AccordionItem>
    )
}
