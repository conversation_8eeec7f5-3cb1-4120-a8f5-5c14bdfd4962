import { GENERAL_REGULATORY_DOCUMENTS, INDUSTRY } from "@/app/create-organization/_services/interfaces"
import { DEFAULT_DOCS } from "../_services/constants"
import { TComplianceDocuments, TFileResponse } from "../_services/interfaces"
import { createContext } from "react"

interface StageFormContext {
    industry: INDUSTRY,
    fileUploaded: (document: GENERAL_REGULATORY_DOCUMENTS, file: TFileResponse) => void,
    removeFile: (document: GENERAL_REGULATORY_DOCUMENTS, fileUrl: string) => void,
    documents: TComplianceDocuments
}

export const StageFormContext = createContext<StageFormContext>({
    industry: INDUSTRY.ECOMMERCE,
    // eslint-disable-next-line
    fileUploaded: (document: GENERAL_REGULATORY_DOCUMENTS, file: TFileResponse) => {},
    // eslint-disable-next-line
    removeFile: (document: GENERAL_REGULATORY_DOCUMENTS, fileUrl: string) => {},
    documents: DEFAULT_DOCS
})