"use client";
// import GlobalSearchBar from "./_components/global-search-bar"
import useOrganizationStore from "@/common/store/use-organization-store";
import { useEffect } from "react";
import { LoaderPinwheel } from "lucide-react";
import { useRouter } from "next/navigation";
import { useGetProfile } from "./(auth)/_services/queries/queries";

export default function Home() {
  const router = useRouter()
  const { organization, setOrganization, setAccount } = useOrganizationStore()
  const { data: response, isLoading} = useGetProfile()

  useEffect(() => {
    if(!organization && response?.data) {
      setAccount(response.data.account)
      setOrganization(response.data.organization)
    }
  }, [response, isLoading, organization, setOrganization, setAccount])

  if(organization) {
    router.push('/dashboard')
  }

  if(isLoading) {
    return (
      <div className="min-h-[60vh] flex items-center justify-center">
        <LoaderPinwheel className="size-24 animate-spin text-paymate-green-50" />
      </div>
    )
  }

  if(!isLoading && response?.statusCode === 404) {
    router.push('/create-organization')
    return (
      <div className="min-h-[60vh] flex items-center justify-center">
        <LoaderPinwheel className="size-24 animate-spin text-paymate-green-50" />
      </div>
    )
  }

  return (
    <h1>Ooopss... Something went wrong, consider refreshing the page</h1>
  );
}
