import ClypButton from "@/common/components/custom/clyp-button";
import { Dialog, DialogContent } from "@/common/components/ui/dialog";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useQueryClient } from "@tanstack/react-query";

interface CreateOrgSuccessProps {
  isOpen: boolean;
  onOpenChange: (value: boolean) => void;
}

export const CreateOrgSuccess = ({
  isOpen,
  onOpenChange,
}: CreateOrgSuccessProps) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  
  const handleGoToDashboard = () => {
    queryClient.invalidateQueries(["get-profile"]);
    router.push('/dashboard');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent 
        className="max-w-sm px-6"
        onEscapeKeyDown={(e) => e.preventDefault()}
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        <section className='pt-10 flex flex-col justify-center items-center gap-5'>
            <Image
                width={180}
                height={180}
                alt="success-icon"
                src="/icons/check-fill.svg"
            />
            <div className="space-y-1 text-center px-7">
                <h1 className='text-bold-32'>Business Created</h1>
                <p className='text-body-2 text-[#575A65] text-center'>Hi jane, you have successfully created your business.</p>
            </div>
            <ClypButton 
                classes="w-[225px] rounded-md h-[56px] text-white"
                text="Go to Dashboard"  
                onClick={handleGoToDashboard}  
            />
        </section>
      </DialogContent>
    </Dialog>
  );
};
