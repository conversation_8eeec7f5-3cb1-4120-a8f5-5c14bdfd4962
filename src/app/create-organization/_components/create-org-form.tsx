"use client"
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/common/components/ui/form'
import { zodResolver } from '@hookform/resolvers/zod';
import React, { useState } from 'react'
import { useForm } from 'react-hook-form';
import { TBasicCreateOrganization } from '../_services/interfaces';
import { BasicCreateOrganizationSchema, CreateOrganzationRequestBody } from '../_services/validations';
import ClypOutlineButton from '@/common/components/custom/clyp-outline-button';
import ClypButton from '@/common/components/custom/clyp-button';
import { useCreateOrgMutation } from '../_services/queries/queries';
import { CreateOrgSuccess } from './create-org-success';
import PlaceholderFloatInput from '@/common/components/custom/placeholder-float-input';
import useOrganizationStore from '@/common/store/use-organization-store';
import { toastErrorMessage } from '@/common/lib/toast-message';
import { useRefreshTokenMutation } from '@/app/(auth)/_services/queries/queries';
import { ACCESS_TOKEN_KEY, REFRESH_TOKEN_KEY } from '@/common/services/constants';
import { LOGIN_MAX_AGE } from '@/common/services/constants';
import { deleteCookie, setCookie } from 'cookies-next';
import { useRouter } from 'next/navigation';

export default function CreateOrgForm() {
    const router = useRouter()
    const [isSuccess, setIsSuccess] = useState(false);
    const { setOrganization } = useOrganizationStore()

    const {mutation} = useCreateOrgMutation()
    const {mutation: refreshTokenMutation} = useRefreshTokenMutation()

    const form = useForm<TBasicCreateOrganization>({
        defaultValues: {
            name: "",
            address: "",
            country: "",
            state: "",
            city: "",
        },
        mode: "onChange",
        resolver: zodResolver(BasicCreateOrganizationSchema),
    });

    const onSubmitHandler = async(data: TBasicCreateOrganization) => {
        return mutation.mutateAsync(CreateOrganzationRequestBody.parse({
            ...data,
            address: `${data.address}, ${data.city}, ${data.state}, ${data.country}`
        })).then(async (response) => {
            setOrganization(response.data)
            setIsSuccess(true)

            await refreshTokenMutation.mutateAsync(undefined)
                                .then((response) => {
                                    setCookie(ACCESS_TOKEN_KEY, response.data.access_token, {
                                        maxAge: LOGIN_MAX_AGE,
                                        secure: true,
                                    });
                                }).catch((error) => {
                                    console.log(error)
                                    deleteCookie(ACCESS_TOKEN_KEY)
                                    deleteCookie(REFRESH_TOKEN_KEY)

                                    toastErrorMessage(error.message ?? 'Failed to re-authorize, please login again')
                                    router.push('/login')
                                })
        }).catch((error) => {
            toastErrorMessage(error.message ?? 'Failed to create organization, please try again')
        })
    }


    return (
        <div>
            <Form {...form}>
                <form
                    className="space-y-20"
                    onSubmit={form.handleSubmit(onSubmitHandler)}
                >
                    <section className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-x-4 md:gap-y-7">
                        <FormField
                            control={form.control}
                            name="name"
                            disabled={mutation.isLoading}
                            render={({ field }) => (
                                <FormItem>
                                    <FormControl>
                                        <PlaceholderFloatInput
                                            name="name"
                                            field={field}
                                            placeholder="Business name"
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="country"
                            disabled={mutation.isLoading}
                            render={({ field }) => (
                                <FormItem>
                                    <FormControl>
                                        <PlaceholderFloatInput
                                            name="country"
                                            field={field}
                                            placeholder="Country"
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="state"
                            disabled={mutation.isLoading}
                            render={({ field }) => (
                                <FormItem>
                                    <FormControl>
                                        <PlaceholderFloatInput
                                            name="state"
                                            field={field}
                                            placeholder="State"
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="city"
                            disabled={mutation.isLoading}
                            render={({ field }) => (
                                <FormItem>
                                    <FormControl>
                                        <PlaceholderFloatInput
                                            name="city"
                                            field={field}
                                            placeholder="City"
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="address"
                            disabled={mutation.isLoading}
                            render={({ field }) => (
                                <FormItem>
                                    <FormControl>
                                        <PlaceholderFloatInput
                                            name="address"
                                            field={field}
                                            placeholder="Address"
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </section>

                    <footer className='flex justify-center'>
                        <div className="w-[420px] flex flex-col gap-y-5">
                            <ClypButton
                                classes='w-full h-[60px] text-white rounded-full'
                                text="Create business"
                                type="submit"
                                isLoading={mutation.isLoading}
                                disabled={!form.formState.isValid || mutation.isLoading}
                            />
                            <ClypOutlineButton
                                classes='w-full h-[60px] text-green-600 rounded-full'
                                text="Cancel"
                                onClick={() => form.clearErrors()}
                                disabled={!form.formState.isValid || mutation.isLoading}
                            />
                        </div>
                    </footer>
                </form>
            </Form>

            <CreateOrgSuccess isOpen={isSuccess} onOpenChange={setIsSuccess} />

        </div>
    )
}
