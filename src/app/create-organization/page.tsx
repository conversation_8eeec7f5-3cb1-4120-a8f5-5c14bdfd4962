import Link from 'next/link'
import React from 'react'
import CreateOrgForm from './_components/create-org-form'
import { Icons } from '@/common/components/custom/icon-templates'

export default function CreateOrganization() {
    return (
        <div className='container pt-20'>
            <Link href="/">
                <Icons.logo />
            </Link>

            <main className='pt-14 space-y-10 max-w-[1047px]'>
                <header className='space-y-1 text-[#25292D]'>
                    <div className="flex items-center gap-4">
                        <span className='text-semibold-16'>Step 1 of ? {" "}</span>
                        <h1 className='text-semibold-24'>Enter Business Information</h1>
                    </div>
                    <p className="text-light-18">Fill in your business name and address below to proceed.</p>
                </header>
        
                <CreateOrgForm />
            </main>
        </div>
    )
}