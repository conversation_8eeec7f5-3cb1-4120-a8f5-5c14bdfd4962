import { z } from "zod";
import { 
    INDUSTRY,
    ECOMMERCE_REGULATORY_DOCUMENTS,
    GAMING_REGULATORY_DOCUMENTS,
    FINANCIAL_SERVICES_REGULATORY_DOCUMENTS
} from "./interfaces";

export const CreateOrganzationRequestBody = z.object({
    name: z
        .string()
        .min(1, {message: 'Business name is required'}),
    address: z
        .string()
        .min(1, {message: 'Password is required'}),
})

export const BasicCreateOrganizationSchema = CreateOrganzationRequestBody.extend({
    country: z
        .string()
        .min(1, {message: 'Country is required'}),
    state: z
        .string()
        .min(1, {message: 'State is required'}),
    city: z
        .string()
        .min(1, {message: 'City is required'}),
})

export const REGULATORY_DOCUMENTS = {
  ...ECOMMERCE_REGULATORY_DOCUMENTS,
  ...FINANCIAL_SERVICES_REGULATORY_DOCUMENTS,
  ...GAMING_REGULATORY_DOCUMENTS,
};

export const REGULATORY_COMPLIANCE_DOCUMENTS_SCHEMA = z.record(
  z.nativeEnum(REGULATORY_DOCUMENTS),
  z.array(z.string()).nonempty(),
);


export const FILE_RESPONSE_SCHEMA = z.object({
  id: z.string().min(1),
  fileName: z.string().min(1),
})

export const DOPED_REGULATORY_COMPLIANCE_DOCUMENTS_SCHEMA = z
  .object({
    [INDUSTRY.ECOMMERCE]: z.record(
      z.nativeEnum(ECOMMERCE_REGULATORY_DOCUMENTS),
      z.array(FILE_RESPONSE_SCHEMA).nonempty(),
    ),
    [INDUSTRY.GAMING]: z.record(
      z.nativeEnum(GAMING_REGULATORY_DOCUMENTS),
      z.array(FILE_RESPONSE_SCHEMA).nonempty(),
    ),
    [INDUSTRY.FINANCIAL_SERVICES]: z.record(
      z.nativeEnum(FINANCIAL_SERVICES_REGULATORY_DOCUMENTS),
      z.array(FILE_RESPONSE_SCHEMA).nonempty(),
    ),
  })
  .partial();