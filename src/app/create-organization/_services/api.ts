import { apiRequest } from "@/common/lib/api-request";
import { TResponse } from "@/common/services/interfaces";
import { TCreateOrganzationRequestBody, TOrganization } from "./interfaces";
import { getRoutes } from "@/common/services/constants";


export const CreateOrganization = async (data: TCreateOrganzationRequestBody) =>
    await apiRequest<TCreateOrganzationRequestBody, TResponse<TOrganization>>({
      path: getRoutes.createOrganization().path,
      method: getRoutes.createOrganization().method,
      payload: data
    });

export const GetMyOrganization = async () =>
    await apiRequest<undefined, TResponse<TOrganization>>({
      path: getRoutes.getOrganization().path,
      method: getRoutes.getOrganization().method,
    });