import { z } from "zod";
import { CreateOrganzationRequestBody, BasicCreateOrganizationSchema, REGULATORY_COMPLIANCE_DOCUMENTS_SCHEMA } from "./validations";
import { BasicTableSchema } from "@/common/services/interfaces";

export type TCreateOrganzationRequestBody = z.infer<typeof CreateOrganzationRequestBody>

export type TBasicCreateOrganization = z.infer<typeof BasicCreateOrganizationSchema>

export enum REGULATORY_COMPLIANCE_STATUS {
    SUBMITTED_FOR_REVIEW = 'submitted_for_review',
    COMPLIANT = 'compliant',
    NON_COMPLIANT = 'non_compliant',
    IN_REVIEW = 'in_review',
    EXEMPT = 'exempt',
}

export enum INDUSTRY {
    ECOMMERCE = 'e-commerce',
    GAMING = 'gaming',
    FINANCIAL_SERVICES = 'financial_services',
}

export enum GENERAL_REGULATORY_DOCUMENTS {
    BUSINESS_REGISTRATION_CERTIFICATE = 'business_registration_certificate',
    BUSINESS_ARTICLES_OF_INCORPORATION = 'business_articles_of_incorporation',
    DIRECTOR_ID_DOC = 'director_id_doc',
}
export enum ECOMMERCE_REGULATORY_DOCUMENTS {
    BUSINESS_REGISTRATION_CERTIFICATE = 'business_registration_certificate',
    BUSINESS_ARTICLES_OF_INCORPORATION = 'business_articles_of_incorporation',
    DIRECTOR_ID_DOC = 'director_id_doc',
}
export enum GAMING_REGULATORY_DOCUMENTS {
    BUSINESS_REGISTRATION_CERTIFICATE = 'business_registration_certificate',
    BUSINESS_ARTICLES_OF_INCORPORATION = 'business_articles_of_incorporation',
    DIRECTOR_ID_DOC = 'director_id_doc',
}
export enum FINANCIAL_SERVICES_REGULATORY_DOCUMENTS {
    BUSINESS_REGISTRATION_CERTIFICATE = 'business_registration_certificate',
    BUSINESS_ARTICLES_OF_INCORPORATION = 'business_articles_of_incorporation',
    DIRECTOR_ID_DOC = 'director_id_doc',
}


export interface TOrganization extends BasicTableSchema {
    name: string;
    address: string;
    industry: INDUSTRY;
    regulatory_compliance_docs: z.infer<typeof REGULATORY_COMPLIANCE_DOCUMENTS_SCHEMA>;
    regulatory_compliance_status: REGULATORY_COMPLIANCE_STATUS;
    regulatory_compliance_review_note: string | null;
    complaince_info_updated_at: Date | null;
}

