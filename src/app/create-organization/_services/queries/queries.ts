import { useMutation, UseMutationResult, useQuery } from "@tanstack/react-query";
import { TCreateOrganzationRequestBody, TOrganization } from "../interfaces";
import { AxiosError } from "axios";
import { TResponse } from "@/common/services/interfaces";
import { CreateOrganization, GetMyOrganization } from "../api";
import { FETCH_ORGANIZATIONS } from "./query-keys";

export const useCreateOrgMutation = () => {
    
    const mutation: UseMutationResult<
        TResponse<TOrganization>,
        AxiosError,
        TCreateOrganzationRequestBody
    > = useMutation({
        mutationKey: ["create-user-organization"],
        mutationFn: CreateOrganization,
    });

    return {mutation, response: mutation.data}
}


export const useMyOrganization = () => {
    
    const query = useQuery({
        queryKey: [String(FETCH_ORGANIZATIONS)],
        queryFn: GetMyOrganization,
        retry: false,
    })
    return query
}