"use client";

import { useState, useEffect } from 'react';
import { ACCESS_TOKEN_KEY, ORGANIZATION_UID_KEY } from '@/common/services/constants';

// Parse cookies from document.cookie string
const parseCookies = () => {
  const cookiesObj: Record<string, string> = {};
  if (typeof document !== 'undefined') {
    const cookies = document.cookie.split(';');
    cookies.forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookiesObj[name] = decodeURIComponent(value);
      }
    });
  }
  return cookiesObj;
};

// Hook to access and manage cookies on the client side
export const useCookies = () => {
  const [cookies, setCookies] = useState<Record<string, string>>({});
  
  useEffect(() => {
    setCookies(parseCookies());
    
    // Update cookies when changes occur
    const handleCookieChange = () => {
      setCookies(parseCookies());
    };
    
    // Listen for cookie changes (custom event)
    window.addEventListener('cookieChange', handleCookieChange);
    
    return () => {
      window.removeEventListener('cookieChange', handleCookieChange);
    };
  }, []);
  
  // Function to get a specific cookie value
  const getCookie = (name: string): string | undefined => {
    return cookies[name];
  };
  
  // Function to set a cookie
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const setCookie = (name: string, value: string, options: Record<string, any> = {}) => {
    const { days = 7, path = '/' } = options;
    
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + days);
    
    const cookieValue = encodeURIComponent(value) +
      '; expires=' + expiryDate.toUTCString() +
      '; path=' + path;
    
    document.cookie = name + '=' + cookieValue;
    
    // Trigger cookie change event
    window.dispatchEvent(new Event('cookieChange'));
  };
  
  // Function to remove a cookie
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const removeCookie = (name: string, options: Record<string, any> = {}) => {
    const { path = '/' } = options;
    document.cookie = name + '=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=' + path;
    
    // Trigger cookie change event
    window.dispatchEvent(new Event('cookieChange'));
  };
  
  // Specific helper for organization UID
  const getOrganizationUid = (): string | undefined => {
    return getCookie(ORGANIZATION_UID_KEY);
  };
  
  // Specific helper for access token
  const getAccessToken = (): string | undefined => {
    return getCookie(ACCESS_TOKEN_KEY);
  };
  
  return {
    cookies,
    getCookie,
    setCookie,
    removeCookie,
    getOrganizationUid,
    getAccessToken
  };
}; 