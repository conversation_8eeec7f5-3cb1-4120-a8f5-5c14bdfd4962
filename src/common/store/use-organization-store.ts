import { TAccountResponse } from "@/app/(auth)/_services/interfaces";
import { TOrganization } from "@/app/create-organization/_services/interfaces";
import { create } from "zustand";

interface AppState {
    account: TAccountResponse | null;
    organization: TOrganization | null;
    setAccount: (account: TAccountResponse) => void;
    setOrganization: (organization: TOrganization) => void;
}

const useOrganizationStore = create<AppState>((set) => ({
    account: null,
    organization: null,
    setAccount: (account) => set({ account }),
    setOrganization: (organization) => set({ organization }),
}))

export default useOrganizationStore;