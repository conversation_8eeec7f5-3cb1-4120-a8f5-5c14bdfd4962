"use client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { useState } from "react";
import { queryStaleTime } from "../services/constants";
import { isProduction } from "../lib/utils";


export const QueryProvider = ({ children }: { children: React.ReactNode }) => {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: queryStaleTime,
            refetchOnWindowFocus: true,
            retry: false,
          },
        },
      })
  );
  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {!isProduction() && (
        <ReactQueryDevtools initialIsOpen={false} position="left" />
      )}
    </QueryClientProvider>
  );
};
