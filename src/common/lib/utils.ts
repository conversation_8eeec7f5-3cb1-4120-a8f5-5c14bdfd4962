import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const isProduction = () => process.env.NODE_ENV === 'production'

export function formatDate(
  date: Date | string | number | undefined | null,
  separator: string = "-" // Default separator to "-"
): string {
  // Handle undefined or null date
  if (!date) {
    return ""; 
  }

  const newDate = new Date(date);

  // Check if the date is valid
  if (isNaN(newDate.getTime())) {
    return ""; // Return empty string for invalid date
  }

  // Extract year, month, and day
  const year = new Intl.DateTimeFormat("en-US", {
    year: "2-digit", // 2-digit year
  }).format(newDate);
  const month = new Intl.DateTimeFormat("en-US", {
    month: "2-digit", // 2-digit month
  }).format(newDate);
  const day = new Intl.DateTimeFormat("en-US", {
    day: "2-digit", // 2-digit day
  }).format(newDate);

  return `${year}${separator}${month}${separator}${day}`;
}