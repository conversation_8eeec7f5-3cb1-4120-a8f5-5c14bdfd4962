"use server";
import axios, { AxiosError, AxiosRequestConfig } from "axios";
import { ACCESS_TOKEN_KEY } from "../services/constants";
import { cookies } from "next/headers";

// Define a structured error response
export interface ApiErrorResponse {
  status: number;
  message: string;
  // eslint-disable-next-line
  data?: any;
  isNetworkError?: boolean;
}

/**
 * Makes an API request using Axios.
 *
 * @param path - The endpoint URL for the API request.
 * @param method - The HTTP method to use for the request (default: "GET").
 * @param payload - The request payload (body) for methods like POST or PUT (default: null).
 * @param params - Query parameters to include in the request URL (default: null).
 * @param headers - Custom headers to include in the request (default: null).
 * @returns The response data from the API request of type `ResponseType`.
 * @throws Will throw an ApiErrorResponse if the request fails.
*/

export type ApiMethod = "GET" | "POST" | "PUT" | "DELETE" | "PATCH"

interface TApiRequest {
  path: string;
  method?: ApiMethod;
  // eslint-disable-next-line
  params?: Record<string, any>;
  // eslint-disable-next-line
  headers?: Record<string, any>;
  externalLink?: boolean;
}

interface ErrorData {
  statusCode: number;
  message: string;
  // eslint-disable-next-line
  data?: any;
}

export async function apiRequest<PayloadType, ResponseType>({
  path,
  method,
  params = {},
  headers = {},
  payload,
  externalLink = false
}: TApiRequest & { payload?: PayloadType }): Promise<ResponseType> {

  const token = (await cookies()).get(ACCESS_TOKEN_KEY)?.value;

  const defaultHeaders = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    "Cache-Control": "no-cache",
    "Authorization": `Bearer ${token}`,
  };

  const baseUrl = process.env.NEXT_PUBLIC_CLIP_BUSINESS_BASE_URL

  const config: AxiosRequestConfig = {
    url: externalLink ? path : `${process.env.NEXT_PUBLIC_CLIP_BUSINESS_BASE_URL}/${path}`,
    method,
    headers: headers ? { ...defaultHeaders, ...headers } : defaultHeaders,
    data: payload,
    params,
  };
  try {
    const response = await axios(config);
    return response.data as ResponseType;

  } catch (error) {
    // console.log('error', error)
    const axiosError = error as AxiosError;

    const errorData = axiosError.response?.data as ErrorData;
    console.log("url", baseUrl, "data----", axiosError);

    throw new Error(errorData.message)

  }
}
