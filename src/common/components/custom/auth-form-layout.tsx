import React from 'react'

export default function AuthFormLayout({children, title, description}: {
    children: React.ReactNode,
    title: string,
    description: string
}) {
  return (
    <div className='space-y-7'>
        <header className='space-y-1 text-dark max-w-[250px] md:max-w-max mx-auto md:mx-0'>
            <h1 className='text-bold-24 text-center md:text-bold-32 md:text-left'>{title}</h1>
            <p className='text-regular-14 text-[#575A65] text-center md:text-light-18 md:text-dark md:text-left'>{description}</p>
        </header>

        <main>{children}</main>
    </div>
  )
}
