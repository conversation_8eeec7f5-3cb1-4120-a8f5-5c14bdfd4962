import { cn } from "@/common/lib/utils";
import { But<PERSON> } from "../ui/button";
import { Input, InputProps } from "../ui/input";
import { EyeIcon, EyeOffIcon } from "lucide-react";
import React from "react";

const PasswordInput = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, ...props }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false);

    return (
      <div className="relative flex items-center border rounded-full">
        <Input
          type={showPassword? "text":"password"}
          className={cn("text-sm md:text-[14px] important block w-full py-3 border-none h-[50px] md:h-[60px] rounded-full placeholder:text-placeholder text-dark", className)}
          ref={ref}
          {...props}
        />
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="h-10 px-3 py-1 hover:bg-transparent"
          onClick={() => setShowPassword((prev) => !prev)}
          disabled={props.value === "" || props.disabled}
        >
          {showPassword ? (
            <EyeOffIcon className="h-4 w-4" aria-hidden="true" />
          ) : (
            <EyeIcon className="h-4 w-4" aria-hidden="true" />
          )}
          <span className="sr-only">
            {showPassword ? "Hide password" : "Show password"}
          </span>
        </Button>
      </div>
    );
  }
);
PasswordInput.displayName = "PasswordInput";

export { PasswordInput };
