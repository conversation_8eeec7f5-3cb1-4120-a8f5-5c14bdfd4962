"use client";
import { cn } from "@/common/lib/utils";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "../ui/tabs";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useCallback } from "react";

interface TTabItem {
    label: React.ReactNode;
    value: string;
    content: React.ReactNode;
    // eslint-disable-next-line
    onClick?: (...args: any[]) => any;
    preventDefault?: boolean;
    hide?: boolean
}

type DefaultValue = string;

interface CustomTabsProps {
    tabs: TTabItem[];
    defaultValue: DefaultValue;
    property: string;
}

export default function CustomTabs({
    tabs,
    defaultValue,
    property,
}: CustomTabsProps) {

    const searchParams = useSearchParams();

    const router = useRouter();
    const searchParamsObj = useSearchParams();

    const initialQuery = searchParams.get(property) || defaultValue

    const handleTabChange = useCallback(
        (key: string) => {
            const newSearchParams = new URLSearchParams(searchParamsObj.toString());
            newSearchParams.set(property, key);

            router.replace(`?${newSearchParams.toString()}`);
        },
        [router, searchParamsObj, property]
    );

    return (
        <Tabs 
            defaultValue={initialQuery}
            className="bg-white"    
        >
            <TabsList className="bg-white">
                {
                    tabs.filter(tab => !tab.hide).map((tab, index) => (
                        <TabsTrigger
                            key={index}
                            value={tab.value}
                            onClick={() => {
                                if (!tab.preventDefault) {
                                    handleTabChange(tab.value)
                                }
                                if (tab.onClick) {
                                    tab.onClick()
                                }
                            }}
                            className={cn(
                                "px-[22px] py-[10px] shadow-none border-orange text-light-20",
                                "data-[state=active]:text-orange data-[state=active]:shadow-none",
                                "data-[state=active]:rounded-none data-[state=active]:border-b-2"
                            )}
                        >
                            {tab.label}
                        </TabsTrigger>
                    ))
                }
            </TabsList>
            {
                tabs.filter(tab => !tab.hide).map((tab, index) => (
                    <TabsContent
                        key={index}
                        value={tab.value}
                        className="mt-0 py-3"
                    >
                        {tab.content}
                    </TabsContent>
                ))
            }
        </Tabs>
    )
}
