"use client"

import { useRouter } from "next/navigation"
import ClypOutlineButton from "./clyp-outline-button"
import { cn } from "@/common/lib/utils"

export default function BackButton({ 
    hideIcon, 
    hideText,
    classes,
    action,
    ...props
}: { 
    hideIcon?: boolean, 
    hideText?: boolean, 
    classes?: string, 
    action?: () => void,
} & React.ButtonHTMLAttributes<HTMLButtonElement>) {
    const router = useRouter()

    return (
        <ClypOutlineButton 
            text="Back"
            icon={'chevronLeft'}
            className={cn(
                "bg-orange-100 text-orange ring-0 h-[40px] w-[90px] rounded-full border-none outline-none", classes, props.className)}
            onClick={() => action ? action : router.back()}
            hideIcon={hideIcon}
            hideText={hideText}
        />
    )
}
