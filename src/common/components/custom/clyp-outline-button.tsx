import React from 'react'
import { But<PERSON> } from '../ui/button'
import { cn } from '@/common/lib/utils'
import { Icons } from './icon-templates'

export default function ClypOutlineButton({
  classes, 
  text, 
  type = 'button',
  icon,
  hideIcon,
  hideText,
  ...props
}: {
    classes?: string, 
    text: string, 
    type?: 'submit'|'button',
    icon?: keyof typeof Icons,
    hideIcon?: boolean,
    hideText?: boolean
} & React.ButtonHTMLAttributes<HTMLButtonElement>) {
  const Icon = icon? Icons[icon] : null
  const RenderIcon = Icon? <Icon /> : null;
  return (
    <Button 
      variant="outline"
      className={cn(`
        bg-inherit hover:bg-initial shadow-none hover:shadow 
        text-orange text-body-2 ring-1 ring-orange`, 
        'flex items-center justify-center gap-2',
        classes, props.className)}
        type={type}
        {...props}
    >
      {!hideIcon ? RenderIcon : ""}
      {!hideText ? text : ""}
    </Button>
  )
}
