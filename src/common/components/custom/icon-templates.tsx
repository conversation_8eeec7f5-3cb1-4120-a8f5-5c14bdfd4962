import { ChevronLeft, ChevronRight, CircleX, SearchIcon } from "lucide-react";

export const Icons = {
    search: (props: React.ComponentProps<typeof SearchIcon>) => <SearchIcon {...props} />,
    closecircle: (props: React.ComponentProps<typeof CircleX>) => <CircleX {...props} />,
    chevronLeft: (props: React.ComponentProps<typeof ChevronLeft>) => <ChevronLeft {...props} />,
    chevronRight: (props: React.ComponentProps<typeof ChevronRight>) => <ChevronRight {...props} />,
    galleryExport: (props: React.ComponentProps<"svg">) => {
        return <svg 
            xmlns="http://www.w3.org/2000/svg" {...props} width="24" height="24" viewBox="0 0 24 24" fill="none"
            {...props}
        >
            <path d="M9 10C10.1046 10 11 9.10457 11 8C11 6.89543 10.1046 6 9 6C7.89543 6 7 6.89543 7 8C7 9.10457 7.89543 10 9 10Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M13 2H9C4 2 2 4 2 9V15C2 20 4 22 9 22H15C20 22 22 20 22 15V10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M18 8V2L20 4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M18 2L16 4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M2.67188 18.9496L7.60187 15.6396C8.39187 15.1096 9.53187 15.1696 10.2419 15.7796L10.5719 16.0696C11.3519 16.7396 12.6119 16.7396 13.3919 16.0696L17.5519 12.4996C18.3319 11.8296 19.5919 11.8296 20.3719 12.4996L22.0019 13.8996" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    },
    logo: (props: React.ComponentProps<"svg">) => (
        <svg width="259" height="75" viewBox="0 0 259 75" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <path d="M29.6071 74.0143C32.6822 74.6714 35.9002 75 39.2611 75C45.5456 75 51.2431 73.895 56.3536 71.6851C61.4641 69.4751 65.7459 66.2638 69.1989 62.0511L58.7774 52.4313C54.3979 57.7377 47.7703 61.12 40.3527 61.12C36.4357 61.12 32.7391 60.1769 29.4775 58.5053L29.6071 74.0143Z" fill="#EA9924"/>
            <path d="M16.4749 37.2422C16.4749 37.9969 16.5099 38.7434 16.5784 39.4802V68.6944C11.8458 65.627 8.01162 61.686 5.07597 56.8715C1.69199 51.1395 0 44.6823 0 37.5C0 30.3177 1.69199 23.895 5.07597 18.232C8.52901 12.5 13.2251 8.04558 19.1644 4.86878C25.1727 1.62293 31.9061 0 39.3646 0C45.6492 0 51.3122 1.10497 56.3536 3.31492C61.4641 5.52486 65.7459 8.70166 69.1989 12.8453L58.969 22.2883C54.5925 16.8469 47.8792 13.3645 40.3527 13.3645C27.1653 13.3645 16.4749 24.0549 16.4749 37.2422Z" fill="#EA9924"/>
            <path d="M40.2992 48.3765C46.4781 48.3765 51.4871 43.3675 51.4871 37.1886C51.4871 31.0098 46.4781 26.0008 40.2992 26.0008C34.1204 26.0008 29.1114 31.0098 29.1114 37.1886C29.1114 43.3675 34.1204 48.3765 40.2992 48.3765Z" fill="#EA9924"/>
            <path d="M115.959 4.85542H110.909V28.8752H115.959V4.85542Z" fill="#EA9924"/>
            <path d="M92.088 27.7746C93.9656 28.7673 96.059 29.2637 98.3681 29.2637C100.332 29.2637 102.112 28.9184 103.709 28.2278C105.306 27.5372 106.645 26.5337 107.724 25.2172L104.357 22.1095C102.825 23.9008 100.926 24.7964 98.6595 24.7964C97.2567 24.7964 96.005 24.4942 94.9044 23.89C93.8037 23.2641 92.9405 22.4009 92.3146 21.3002C91.7104 20.1996 91.4082 18.9479 91.4082 17.5451C91.4082 16.1423 91.7104 14.8906 92.3146 13.79C92.9405 12.6894 93.8037 11.8369 94.9044 11.2326C96.005 10.6068 97.2567 10.2939 98.6595 10.2939C100.926 10.2939 102.825 11.1787 104.357 12.9483L107.724 9.84066C106.645 8.54579 105.306 7.55306 103.709 6.86246C102.134 6.17187 100.364 5.82657 98.4005 5.82657C96.0698 5.82657 93.9656 6.33373 92.088 7.34804C90.2321 8.34077 88.7645 9.73275 87.6855 11.524C86.628 13.2936 86.0993 15.3007 86.0993 17.5451C86.0993 19.7896 86.628 21.8074 87.6855 23.5986C88.7645 25.3683 90.2321 26.7603 92.088 27.7746Z" fill="#EA9924"/>
            <path d="M137.932 11.4592L130.066 29.9435C129.267 31.9505 128.275 33.3641 127.088 34.1842C125.922 35.0042 124.509 35.4143 122.847 35.4143C121.941 35.4143 121.045 35.274 120.16 34.9935C119.275 34.7129 118.552 34.3244 117.991 33.8281L119.836 30.2348C120.225 30.5801 120.667 30.8499 121.164 31.0441C121.682 31.2383 122.189 31.3355 122.685 31.3355C123.376 31.3355 123.937 31.1628 124.368 30.8175C124.8 30.4938 125.189 29.9435 125.534 29.1666L125.599 29.0047L118.056 11.4592H123.268L128.156 23.2749L133.076 11.4592H137.932Z" fill="#EA9924"/>
            <path fillRule="evenodd" clipRule="evenodd" d="M154.69 12.3333C153.373 11.5779 151.906 11.2003 150.287 11.2003C147.892 11.2003 146.068 11.9556 144.816 13.4663V11.4592H139.993V35.1553H145.043V27.03C146.316 28.4328 148.064 29.1342 150.287 29.1342C151.906 29.1342 153.373 28.7673 154.69 28.0335C156.028 27.2782 157.074 26.2315 157.83 24.8935C158.585 23.5339 158.963 21.9585 158.963 20.1672C158.963 18.376 158.585 16.8114 157.83 15.4733C157.074 14.1137 156.028 13.067 154.69 12.3333ZM152.585 23.6957C151.765 24.559 150.708 24.9906 149.413 24.9906C148.118 24.9906 147.05 24.559 146.208 23.6957C145.388 22.8109 144.978 21.6347 144.978 20.1672C144.978 18.6997 145.388 17.5343 146.208 16.6711C147.05 15.7863 148.118 15.3438 149.413 15.3438C150.708 15.3438 151.765 15.7863 152.585 16.6711C153.427 17.5343 153.848 18.6997 153.848 20.1672C153.848 21.6347 153.427 22.8109 152.585 23.6957Z" fill="#EA9924"/>
            <path fillRule="evenodd" clipRule="evenodd" d="M181.676 6.21503C183.683 6.21503 185.42 6.54954 186.888 7.21855C188.377 7.88757 189.52 8.83714 190.319 10.0673C191.117 11.2974 191.517 12.7541 191.517 14.4374C191.517 16.0992 191.117 17.5559 190.319 18.8076C189.52 20.0377 188.377 20.9873 186.888 21.6563C185.42 22.3038 183.683 22.6275 181.676 22.6275H177.111V28.8752H171.867V6.21503H181.676ZM181.384 18.3544C182.96 18.3544 184.158 18.0199 184.978 17.3509C185.798 16.6603 186.208 15.6891 186.208 14.4374C186.208 13.1642 185.798 12.193 184.978 11.524C184.158 10.8334 182.96 10.4881 181.384 10.4881H177.111V18.3544H181.384Z" fill="#EA9924"/>
            <path fillRule="evenodd" clipRule="evenodd" d="M207.987 13.1426C206.541 11.8477 204.469 11.2003 201.771 11.2003C200.368 11.2003 198.998 11.3837 197.66 11.7506C196.344 12.1175 195.21 12.6354 194.261 13.3044L196.074 16.8329C196.7 16.3366 197.455 15.9481 198.34 15.6676C199.246 15.3654 200.163 15.2144 201.091 15.2144C202.429 15.2144 203.433 15.5165 204.102 16.1208C204.771 16.7035 205.105 17.5451 205.105 18.6458H201.091C198.545 18.6458 196.656 19.1098 195.426 20.0377C194.218 20.9657 193.613 22.239 193.613 23.8576C193.613 24.8719 193.872 25.7783 194.39 26.5768C194.908 27.3753 195.653 28.0012 196.624 28.4544C197.617 28.9076 198.782 29.1342 200.12 29.1342C202.71 29.1342 204.48 28.3249 205.429 26.7063V28.8752H210.155V18.9371C210.155 16.3474 209.432 14.4159 207.987 13.1426ZM203.681 25.1525C203.012 25.5409 202.246 25.7352 201.383 25.7352C200.498 25.7352 199.796 25.5517 199.279 25.1848C198.782 24.7964 198.534 24.2784 198.534 23.631C198.534 22.2714 199.57 21.5916 201.642 21.5916H205.105V23.372C204.825 24.1489 204.35 24.7424 203.681 25.1525Z" fill="#EA9924"/>
            <path d="M231.696 11.4592L223.83 29.9435C223.031 31.9505 222.039 33.3641 220.852 34.1842C219.686 35.0042 218.273 35.4143 216.611 35.4143C215.705 35.4143 214.809 35.274 213.924 34.9935C213.039 34.7129 212.316 34.3244 211.755 33.8281L213.6 30.2348C213.989 30.5801 214.431 30.8499 214.928 31.0441C215.446 31.2383 215.953 31.3355 216.449 31.3355C217.14 31.3355 217.701 31.1628 218.133 30.8175C218.564 30.4938 218.953 29.9435 219.298 29.1666L219.363 29.0047L211.82 11.4592H217.032L221.92 23.2749L226.84 11.4592H231.696Z" fill="#EA9924"/>
            <path d="M159.865 42.9777C159.865 44.8579 161.362 46.316 163.587 46.316C165.813 46.316 167.309 44.8579 167.309 42.8626C167.309 41.0208 165.813 39.6394 163.587 39.6394C161.362 39.6394 159.865 41.0975 159.865 42.9777Z" fill="#EA9924"/>
            <path d="M160.594 49.1938V69.8376H166.58V49.1938H160.594Z" fill="#EA9924"/>
            <path fillRule="evenodd" clipRule="evenodd" d="M110.734 62.5087C110.734 59.132 108.815 56.8297 105.822 55.8705C108.009 54.7193 109.391 52.6473 109.391 49.9613C109.391 45.7788 105.937 42.9777 99.2223 42.9777H86.0993V69.8376H99.9897C107.05 69.8376 110.734 67.1516 110.734 62.5087ZM92.2771 47.659H98.4548C101.486 47.659 103.136 48.695 103.136 50.8054C103.136 52.9159 101.486 53.9903 98.4548 53.9903H92.2771V47.659ZM104.479 61.8564C104.479 64.1587 102.752 65.1563 99.5292 65.1563H92.2771V58.5181H99.5292C102.752 58.5181 104.479 59.5925 104.479 61.8564Z" fill="#EA9924"/>
            <path d="M135.431 49.1938H129.445V59.4006C129.445 63.1994 127.45 64.9645 124.687 64.9645C122.04 64.9645 120.505 63.4296 120.505 59.9762V49.1938H114.519V60.8587C114.519 67.2284 118.164 70.1446 123.344 70.1446C125.877 70.1446 128.179 69.1853 129.752 67.3818V69.8376H135.431V49.1938Z" fill="#EA9924"/>
            <path d="M147.538 70.1446C144.047 70.1446 140.516 69.1853 138.598 67.919L140.593 63.6215C142.435 64.811 145.236 65.6168 147.769 65.6168C150.531 65.6168 151.567 64.8877 151.567 63.7366C151.567 62.4191 149.669 62.1414 147.354 61.8026C143.735 61.2731 139.097 60.5946 139.097 55.5635C139.097 51.6496 142.627 48.8869 148.651 48.8869C151.491 48.8869 154.637 49.5392 156.594 50.6903L154.599 54.9495C152.565 53.7984 150.531 53.4147 148.651 53.4147C145.965 53.4147 144.814 54.2589 144.814 55.3333C144.814 56.7352 146.781 57.0418 149.152 57.4114C152.752 57.9725 157.285 58.679 157.285 63.5831C157.285 67.4202 153.716 70.1446 147.538 70.1446Z" fill="#EA9924"/>
            <path d="M184.299 48.8869C189.172 48.8869 192.856 51.7263 192.856 58.0192V69.8376H186.87V58.9402C186.87 55.6019 185.335 54.067 182.688 54.067C179.81 54.067 177.738 55.8321 177.738 59.6308V69.8376H171.752V49.1938H177.469V51.6112C179.081 49.8462 181.498 48.8869 184.299 48.8869Z" fill="#EA9924"/>
            <path fillRule="evenodd" clipRule="evenodd" d="M218.27 60.737C218.306 60.3412 218.345 59.9185 218.345 59.5925C218.345 52.9542 213.663 48.8869 207.601 48.8869C201.308 48.8869 196.588 53.3379 196.588 59.5157C196.588 65.6551 201.231 70.1446 208.368 70.1446C212.09 70.1446 214.968 68.9934 216.887 66.8063L213.702 63.3529C212.282 64.6959 210.709 65.3482 208.522 65.3482C205.375 65.3482 203.188 63.7749 202.613 61.2041H218.23C218.241 61.0587 218.255 60.8999 218.27 60.737ZM202.536 57.7123C202.958 55.0647 204.915 53.4147 207.639 53.4147C210.325 53.4147 212.282 55.103 212.704 57.7123H202.536Z" fill="#EA9924"/>
            <path d="M228.892 70.1446C225.4 70.1446 221.87 69.1853 219.951 67.919L221.946 63.6215C223.788 64.811 226.589 65.6168 229.122 65.6168C231.884 65.6168 232.92 64.8877 232.92 63.7366C232.92 62.4191 231.022 62.1414 228.707 61.8026C225.088 61.2731 220.45 60.5946 220.45 55.5635C220.45 51.6496 223.98 48.8869 230.004 48.8869C232.844 48.8869 235.99 49.5392 237.947 50.6903L235.952 54.9495C233.918 53.7984 231.884 53.4147 230.004 53.4147C227.318 53.4147 226.167 54.2589 226.167 55.3333C226.167 56.7352 228.134 57.0418 230.506 57.4114C234.105 57.9725 238.638 58.679 238.638 63.5831C238.638 67.4202 235.069 70.1446 228.892 70.1446Z" fill="#EA9924"/>
            <path d="M239.952 67.919C241.871 69.1853 245.401 70.1446 248.893 70.1446C255.07 70.1446 258.639 67.4202 258.639 63.5831C258.639 58.679 254.107 57.9725 250.507 57.4114C248.135 57.0418 246.168 56.7352 246.168 55.3333C246.168 54.2589 247.319 53.4147 250.005 53.4147C251.885 53.4147 253.919 53.7984 255.953 54.9495L257.948 50.6903C255.991 49.5392 252.845 48.8869 250.005 48.8869C243.981 48.8869 240.451 51.6496 240.451 55.5635C240.451 60.5946 245.089 61.2731 248.708 61.8026C251.023 62.1414 252.922 62.4191 252.922 63.7366C252.922 64.8877 251.886 65.6168 249.123 65.6168C246.59 65.6168 243.789 64.811 241.947 63.6215L239.952 67.919Z" fill="#EA9924"/>
        </svg>
    ),
    importCloud: (props: React.ComponentProps<"svg">) => (
        <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props} {...props}>
        <g clip-path="url(#clip0_12459_93818)">
        <path d="M7.50142 18.0002C6.2537 18.0002 5.05708 17.5261 4.1748 16.6822C3.29253 15.8383 2.79687 14.6937 2.79688 13.5002C2.79687 12.3067 3.29253 11.1621 4.1748 10.3182C5.05708 9.4743 6.2537 9.0002 7.50142 9.0002C7.79611 7.68737 8.65818 6.53368 9.89801 5.79291C10.5119 5.42612 11.2001 5.17174 11.9232 5.04431C12.6463 4.91687 13.3903 4.91887 14.1125 5.0502C14.8348 5.18152 15.5213 5.43959 16.1327 5.80968C16.7442 6.17976 17.2686 6.65461 17.6762 7.20712C18.0837 7.75963 18.3664 8.37898 18.508 9.02979C18.6496 9.68061 18.6473 10.3502 18.5014 11.0002H19.5014C20.4297 11.0002 21.3199 11.3689 21.9763 12.0253C22.6327 12.6817 23.0014 13.5719 23.0014 14.5002C23.0014 15.4285 22.6327 16.3187 21.9763 16.9751C21.3199 17.6314 20.4297 18.0002 19.5014 18.0002H18.5014" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M9.5 15L12.5 12L15.5 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M12.5 12V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </g>
        <defs>
        <clipPath id="clip0_12459_93818">
        <rect width="24" height="24" fill="white" transform="translate(0.5)"/>
        </clipPath>
        </defs>
        </svg>
    ),
    success: (props: React.ComponentProps<"svg">) => (
        <svg width="212" height="212" viewBox="0 0 212 212" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <rect width="212" height="212" rx="100" fill="#008F2D"/>
        <path d="M56.5625 120.135L81.2958 144.869L155.496 67.1353" stroke="white" strokeWidth="15.9" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    ),
    cloudUpload: (props: React.ComponentProps<"svg">) => (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path d="M11.2525 13.3166C12.1978 13.3236 13.1078 12.9709 13.8062 12.336C16.113 10.3185 14.8785 6.26927 11.838 5.88833C10.7516 -0.700457 1.2494 1.79679 3.49974 8.06813" stroke="currentColor" strokeWidth="1.2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M4.66916 8.35772C4.29528 8.16726 3.87907 8.06849 3.46286 8.07555C0.175521 8.30834 0.182575 13.0912 3.46286 13.324" stroke="currentColor" strokeWidth="1.2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M10.6953 6.18474C11.0621 6.00132 11.4572 5.90256 11.8663 5.89551" stroke="currentColor" strokeWidth="1.2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M7.27393 14.727V9.90527M7.27393 9.90527L5.67969 11.4995M7.27393 9.90527L8.86816 11.4995" stroke="currentColor" strokeWidth="1.2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    ),

    dashboard: (props: React.ComponentProps<"svg">) => (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props} {...props}>
        <path d="M2 18C2 16.4596 2 15.6893 2.34673 15.1235C2.54074 14.8069 2.80693 14.5407 3.12353 14.3467C3.68934 14 4.45956 14 6 14C7.54044 14 8.31066 14 8.87647 14.3467C9.19307 14.5407 9.45926 14.8069 9.65327 15.1235C10 15.6893 10 16.4596 10 18C10 19.5404 10 20.3107 9.65327 20.8765C9.45926 21.1931 9.19307 21.4593 8.87647 21.6533C8.31066 22 7.54044 22 6 22C4.45956 22 3.68934 22 3.12353 21.6533C2.80693 21.4593 2.54074 21.1931 2.34673 20.8765C2 20.3107 2 19.5404 2 18Z" stroke="currentColor" strokeWidth="1.5"/>
        <path d="M14 18C14 16.4596 14 15.6893 14.3467 15.1235C14.5407 14.8069 14.8069 14.5407 15.1235 14.3467C15.6893 14 16.4596 14 18 14C19.5404 14 20.3107 14 20.8765 14.3467C21.1931 14.5407 21.4593 14.8069 21.6533 15.1235C22 15.6893 22 16.4596 22 18C22 19.5404 22 20.3107 21.6533 20.8765C21.4593 21.1931 21.1931 21.4593 20.8765 21.6533C20.3107 22 19.5404 22 18 22C16.4596 22 15.6893 22 15.1235 21.6533C14.8069 21.4593 14.5407 21.1931 14.3467 20.8765C14 20.3107 14 19.5404 14 18Z" stroke="currentColor" strokeWidth="1.5"/>
        <path d="M2 6C2 4.45956 2 3.68934 2.34673 3.12353C2.54074 2.80693 2.80693 2.54074 3.12353 2.34673C3.68934 2 4.45956 2 6 2C7.54044 2 8.31066 2 8.87647 2.34673C9.19307 2.54074 9.45926 2.80693 9.65327 3.12353C10 3.68934 10 4.45956 10 6C10 7.54044 10 8.31066 9.65327 8.87647C9.45926 9.19307 9.19307 9.45926 8.87647 9.65327C8.31066 10 7.54044 10 6 10C4.45956 10 3.68934 10 3.12353 9.65327C2.80693 9.45926 2.54074 9.19307 2.34673 8.87647C2 8.31066 2 7.54044 2 6Z" stroke="currentColor" strokeWidth="1.5"/>
        <path d="M14 6C14 4.45956 14 3.68934 14.3467 3.12353C14.5407 2.80693 14.8069 2.54074 15.1235 2.34673C15.6893 2 16.4596 2 18 2C19.5404 2 20.3107 2 20.8765 2.34673C21.1931 2.54074 21.4593 2.80693 21.6533 3.12353C22 3.68934 22 4.45956 22 6C22 7.54044 22 8.31066 21.6533 8.87647C21.4593 9.19307 21.1931 9.45926 20.8765 9.65327C20.3107 10 19.5404 10 18 10C16.4596 10 15.6893 10 15.1235 9.65327C14.8069 9.45926 14.5407 9.19307 14.3467 8.87647C14 8.31066 14 7.54044 14 6Z" stroke="currentColor" strokeWidth="1.5"/>
        </svg>
    ),
    transfer: (props: React.ComponentProps<"svg">) => (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props} {...props}>
        <path d="M19 9H6.65856C5.65277 9 5.14987 9 5.02472 8.69134C4.89957 8.38268 5.25517 8.01942 5.96637 7.29289L8.21091 5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M5 15H17.3414C18.3472 15 18.8501 15 18.9753 15.3087C19.1004 15.6173 18.7448 15.9806 18.0336 16.7071L15.7891 19" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    ),
    profileCircle: (props: React.ComponentProps<"svg">) => (
        <svg width="188" height="188" viewBox="0 0 188 188" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path d="M94.938 100.11C94.3897 100.032 93.6847 100.032 93.058 100.11C79.2714 99.6401 68.3047 88.3601 68.3047 74.4951C68.3047 60.3168 79.7414 48.8018 93.998 48.8018C108.176 48.8018 119.691 60.3168 119.691 74.4951C119.613 88.3601 108.725 99.6401 94.938 100.11Z" stroke="#25292D" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M146.796 151.81C132.853 164.578 114.366 172.333 93.9998 172.333C73.6331 172.333 55.1465 164.578 41.2031 151.81C41.9865 144.446 46.6865 137.24 55.0681 131.6C76.5315 117.343 111.625 117.343 132.931 131.6C141.313 137.24 146.013 144.446 146.796 151.81Z" stroke="#25292D" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M93.9974 172.334C137.26 172.334 172.331 137.263 172.331 94.0003C172.331 50.738 137.26 15.667 93.9974 15.667C50.7351 15.667 15.6641 50.738 15.6641 94.0003C15.6641 137.263 50.7351 172.334 93.9974 172.334Z" stroke="#25292D" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    ),
    users: (props: React.ComponentProps<"svg">) => (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path d="M18.6161 20H19.1063C20.2561 20 21.1707 19.4761 21.9919 18.7436C24.078 16.8826 19.1741 15 17.5 15M15.5 5.06877C15.7271 5.02373 15.9629 5 16.2048 5C18.0247 5 19.5 6.34315 19.5 8C19.5 9.65685 18.0247 11 16.2048 11C15.9629 11 15.7271 10.9763 15.5 10.9312" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
        <path d="M4.48131 16.1112C3.30234 16.743 0.211137 18.0331 2.09388 19.6474C3.01359 20.436 4.03791 21 5.32572 21H12.6743C13.9621 21 14.9864 20.436 15.9061 19.6474C17.7889 18.0331 14.6977 16.743 13.5187 16.1112C10.754 14.6296 7.24599 14.6296 4.48131 16.1112Z" stroke="currentColor" strokeWidth="1.5"/>
        <path d="M13 7.5C13 9.70914 11.2091 11.5 9 11.5C6.79086 11.5 5 9.70914 5 7.5C5 5.29086 6.79086 3.5 9 3.5C11.2091 3.5 13 5.29086 13 7.5Z" stroke="currentColor" strokeWidth="1.5"/>
        </svg>
    ),
    wallet: (props: React.ComponentProps<"svg">) => (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path d="M10.5495 2.09877L10.5245 2.15711L8.10781 7.76544H5.73281C5.16615 7.76544 4.62448 7.88211 4.13281 8.09044L5.59115 4.60711L5.62448 4.52377L5.68281 4.39044C5.69948 4.34044 5.71615 4.29044 5.74115 4.24877C6.83281 1.72377 8.06615 1.14877 10.5495 2.09877Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M15.0427 7.93197C14.6677 7.8153 14.2677 7.7653 13.8677 7.7653H8.10938L10.526 2.15697L10.551 2.09863C10.676 2.1403 10.7927 2.19863 10.9177 2.24863L12.7594 3.02363C13.7844 3.44863 14.501 3.8903 14.9344 4.42363C15.0177 4.52363 15.0844 4.6153 15.1427 4.72363C15.2177 4.8403 15.276 4.95697 15.3094 5.08197C15.3427 5.15697 15.3677 5.23197 15.3844 5.29863C15.6094 5.99863 15.476 6.85697 15.0427 7.93197Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M17.938 11.8323V13.4573C17.938 13.624 17.9297 13.7906 17.9214 13.9573C17.763 16.8656 16.138 18.3323 13.0547 18.3323H6.55469C6.35469 18.3323 6.15469 18.3156 5.96302 18.2906C3.31302 18.1156 1.89635 16.699 1.72135 14.049C1.69635 13.8573 1.67969 13.6573 1.67969 13.4573V11.8323C1.67969 10.1573 2.69635 8.71563 4.14635 8.09063C4.64635 7.88229 5.17969 7.76562 5.74635 7.76562H13.8797C14.288 7.76562 14.688 7.82396 15.0547 7.93229C16.713 8.44063 17.938 9.99063 17.938 11.8323Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M5.58906 4.60645L4.13073 8.08978C2.68073 8.71478 1.66406 10.1564 1.66406 11.8314V9.38978C1.66406 7.02311 3.3474 5.04811 5.58906 4.60645Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M17.9302 9.38939V11.8311C17.9302 9.99772 16.7135 8.43939 15.0469 7.93939C15.4802 6.85605 15.6052 6.00605 15.3969 5.29772C15.3802 5.22272 15.3552 5.14772 15.3219 5.08105C16.8719 5.88105 17.9302 7.52272 17.9302 9.38939Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    ),
    moneys: (props: React.ComponentProps<"svg">) => (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path d="M16.0802 6.60023V10.8919C16.0802 13.4586 14.6135 14.5586 12.4135 14.5586H5.08854C4.71354 14.5586 4.3552 14.5253 4.02187 14.4503C3.81354 14.4169 3.61354 14.3586 3.43021 14.2919C2.18021 13.8253 1.42188 12.7419 1.42188 10.8919V6.60023C1.42188 4.03356 2.88854 2.93359 5.08854 2.93359H12.4135C14.2802 2.93359 15.6219 3.72526 15.9802 5.53359C16.0385 5.86692 16.0802 6.20856 16.0802 6.60023Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M18.5818 9.09984V13.3915C18.5818 15.9582 17.1151 17.0582 14.9151 17.0582H7.5901C6.97344 17.0582 6.41511 16.9749 5.93178 16.7915C4.94011 16.4249 4.2651 15.6665 4.02344 14.4499C4.35677 14.5249 4.7151 14.5582 5.0901 14.5582H12.4151C14.6151 14.5582 16.0818 13.4582 16.0818 10.8915V6.59984C16.0818 6.20818 16.0484 5.8582 15.9818 5.5332C17.5651 5.86654 18.5818 6.98318 18.5818 9.09984Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M8.74689 10.9498C9.96191 10.9498 10.9469 9.96484 10.9469 8.74982C10.9469 7.53479 9.96191 6.5498 8.74689 6.5498C7.53186 6.5498 6.54688 7.53479 6.54688 8.74982C6.54688 9.96484 7.53186 10.9498 8.74689 10.9498Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M3.97656 6.91699V10.5837" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M13.5156 6.91699V10.5837" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    ),
    link: (props: React.ComponentProps<"svg">) => (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path d="M16.0802 6.60023V10.8919C16.0802 13.4586 14.6135 14.5586 12.4135 14.5586H5.08854C4.71354 14.5586 4.3552 14.5253 4.02187 14.4503C3.81354 14.4169 3.61354 14.3586 3.43021 14.2919C2.18021 13.8253 1.42188 12.7419 1.42188 10.8919V6.60023C1.42188 4.03356 2.88854 2.93359 5.08854 2.93359H12.4135C14.2802 2.93359 15.6219 3.72526 15.9802 5.53359C16.0385 5.86692 16.0802 6.20856 16.0802 6.60023Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M18.5818 9.09984V13.3915C18.5818 15.9582 17.1151 17.0582 14.9151 17.0582H7.5901C6.97344 17.0582 6.41511 16.9749 5.93178 16.7915C4.94011 16.4249 4.2651 15.6665 4.02344 14.4499C4.35677 14.5249 4.7151 14.5582 5.0901 14.5582H12.4151C14.6151 14.5582 16.0818 13.4582 16.0818 10.8915V6.59984C16.0818 6.20818 16.0484 5.8582 15.9818 5.5332C17.5651 5.86654 18.5818 6.98318 18.5818 9.09984Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M8.74689 10.9498C9.96191 10.9498 10.9469 9.96484 10.9469 8.74982C10.9469 7.53479 9.96191 6.5498 8.74689 6.5498C7.53186 6.5498 6.54688 7.53479 6.54688 8.74982C6.54688 9.96484 7.53186 10.9498 8.74689 10.9498Z" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M3.97656 6.91699V10.5837" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M13.5156 6.91699V10.5837" stroke="currentColor" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    ),
    reciept: (props: React.ComponentProps<"svg">) => (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path d="M5.60573 16.417C6.28906 15.6837 7.33073 15.742 7.93073 16.542L8.7724 17.667C9.4474 18.5587 10.5391 18.5587 11.2141 17.667L12.0557 16.542C12.6557 15.742 13.6974 15.6837 14.3807 16.417C15.8641 18.0003 17.0724 17.4753 17.0724 15.2587V5.86699C17.0807 2.50866 16.2974 1.66699 13.1474 1.66699H6.8474C3.6974 1.66699 2.91406 2.50866 2.91406 5.86699V15.2503C2.91406 17.4753 4.13073 17.992 5.60573 16.417Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M6.66406 5.83301H13.3307" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M7.5 9.16699H12.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    ),
    send: (props: React.ComponentProps<"svg">) => (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path d="M7.4974 18.3337H12.4974C16.6641 18.3337 18.3307 16.667 18.3307 12.5003V7.50033C18.3307 3.33366 16.6641 1.66699 12.4974 1.66699H7.4974C3.33073 1.66699 1.66406 3.33366 1.66406 7.50033V12.5003C1.66406 16.667 3.33073 18.3337 7.4974 18.3337Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M8.82031 6.40039H12.3537V9.94206" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M12.3573 6.40039L7.64062 11.1171" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M5 13.7588C8.24167 14.8421 11.7583 14.8421 15 13.7588" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    ),
    settings: (props: React.ComponentProps<"svg">) => (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path d="M17.7672 5.95084L17.3558 5.237C17.0448 4.69714 16.8892 4.42721 16.6246 4.31958C16.3599 4.21194 16.0606 4.29687 15.462 4.46674L14.4452 4.75316C14.063 4.84129 13.662 4.79129 13.3131 4.612L13.0323 4.45002C12.7331 4.25837 12.5029 3.97579 12.3755 3.64363L12.0972 2.81247C11.9143 2.26246 11.8228 1.98745 11.605 1.83015C11.3871 1.67285 11.0978 1.67285 10.5192 1.67285H9.59022C9.01159 1.67285 8.72227 1.67285 8.50446 1.83015C8.28665 1.98745 8.19516 2.26246 8.01218 2.81247L7.73388 3.64363C7.60648 3.97579 7.37631 4.25837 7.07708 4.45002L6.79634 4.612C6.44739 4.79129 6.04643 4.84129 5.66426 4.75316L4.64739 4.46674C4.04878 4.29687 3.74948 4.21194 3.48483 4.31958C3.22018 4.42721 3.06464 4.69714 2.75357 5.237L2.34224 5.95084C2.05065 6.45688 1.90486 6.7099 1.93315 6.97925C1.96145 7.2486 2.15663 7.46566 2.54699 7.89977L3.40619 8.86036C3.61619 9.12621 3.76528 9.58952 3.76528 10.006C3.76528 10.4229 3.61624 10.886 3.40621 11.1519L2.54699 12.1125C2.15663 12.5466 1.96145 12.7637 1.93315 13.0331C1.90486 13.3024 2.05066 13.5554 2.34224 14.0615L2.75355 14.7753C3.06463 15.3151 3.22018 15.5851 3.48483 15.6927C3.74948 15.8004 4.04879 15.7154 4.64741 15.5455L5.66422 15.2591C6.04646 15.171 6.44749 15.221 6.79649 15.4003L7.07719 15.5623C7.37636 15.754 7.60647 16.0365 7.73385 16.3687L8.01218 17.1999C8.19516 17.7499 8.28665 18.0249 8.50446 18.1822C8.72227 18.3395 9.01159 18.3395 9.59022 18.3395H10.5192C11.0978 18.3395 11.3871 18.3395 11.605 18.1822C11.8228 18.0249 11.9143 17.7499 12.0972 17.1999L12.3756 16.3687C12.5029 16.0365 12.7331 15.754 13.0322 15.5623L13.3129 15.4003C13.6619 15.221 14.063 15.171 14.4452 15.2591L15.462 15.5455C16.0606 15.7154 16.3599 15.8004 16.6246 15.6927C16.8892 15.5851 17.0448 15.3151 17.3559 14.7753L17.3559 14.7753L17.7672 14.0615C18.0588 13.5554 18.2046 13.3024 18.1763 13.0331C18.148 12.7637 17.9528 12.5466 17.5624 12.1125L16.7032 11.1519C16.4932 10.886 16.3441 10.4229 16.3441 10.006C16.3441 9.58952 16.4932 9.12621 16.7032 8.86036L17.5624 7.89977C17.9528 7.46566 18.148 7.2486 18.1763 6.97925C18.2046 6.7099 18.0588 6.45688 17.7672 5.95084Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
        <path d="M12.9349 9.99967C12.9349 11.6105 11.6291 12.9163 10.0182 12.9163C8.4074 12.9163 7.10156 11.6105 7.10156 9.99967C7.10156 8.38884 8.4074 7.08301 10.0182 7.08301C11.6291 7.08301 12.9349 8.38884 12.9349 9.99967Z" stroke="currentColor" strokeWidth="1.5"/>
        </svg>
    ),
    powerOff: (props: React.ComponentProps<"svg">) => (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path d="M5 5.41699C3.46563 6.79029 2.5 8.61236 2.5 10.8336C2.5 14.9757 5.85786 18.3336 10 18.3336C14.1421 18.3336 17.5 14.9757 17.5 10.8336C17.5 8.61236 16.5344 6.79029 15 5.41699" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M9.9974 1.66699V9.16699M9.9974 1.66699C9.41387 1.66699 8.32367 3.32891 7.91406 3.75033M9.9974 1.66699C10.5809 1.66699 11.6711 3.32891 12.0807 3.75033" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    ),
    trash: (props: React.ComponentProps<"svg">) => (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path d="M21 5.98047C17.67 5.65047 14.32 5.48047 10.98 5.48047C9 5.48047 7.02 5.58047 5.04 5.78047L3 5.98047" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M8.5 4.97L8.72 3.66C8.88 2.71 9 2 10.69 2H13.31C15 2 15.13 2.75 15.28 3.67L15.5 4.97" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M18.8484 9.13965L18.1984 19.2096C18.0884 20.7796 17.9984 21.9996 15.2084 21.9996H8.78844C5.99844 21.9996 5.90844 20.7796 5.79844 19.2096L5.14844 9.13965" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M10.3281 16.5H13.6581" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M9.5 12.5H14.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    ),
    bitcoin: (props: React.ComponentProps<"svg">) => (
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" {...props}>
        <rect width="32" height="32" rx="16" fill="url(#pattern0_14091_23914)"/>
        <defs>
        <pattern id="pattern0_14091_23914" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_14091_23914" transform="scale(0.03125)"/>
        </pattern>
        <image id="image0_14091_23914" width="32" height="32" preserveAspectRatio="none" xlinkHref="data:image/png;base64,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"/>
        </defs>
</svg>

    ),
    ethereum: (props: React.ComponentProps<"svg">) => (
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" {...props}>
        <rect width="32" height="32" rx="16" fill="url(#pattern0_14091_26103)"/>
        <defs>
        <pattern id="pattern0_14091_26103" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_14091_26103" transform="scale(0.03125)"/>
        </pattern>
        <image id="image0_14091_26103" width="32" height="32" preserveAspectRatio="none" xlinkHref="data:image/png;base64,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"/>
        </defs>
        </svg>
    ),
    usdt: (props: React.ComponentProps<"svg">) => (
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" {...props}>
        <rect width="32" height="32" rx="16" fill="url(#pattern0_14091_1051)"/>
        <defs>
        <pattern id="pattern0_14091_1051" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_14091_1051" transform="scale(0.03125)"/>
        </pattern>
        <image id="image0_14091_1051" width="32" height="32" preserveAspectRatio="none" xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAPSSURBVHgBtVdLTBNBGP5nWrbFJ741CpUU4oEEDiZAjPJIhATiA0zkpPECF8EHF08cMPHgwQQQOOnFwI1EiQdIwIRHOIAnJZCIgFiQKIIKiiktZdb/X9mllbY7K+VLNp3t/vN//8z8r2EgCVUFVjkJbm535oKAAhVENgPm0r6B6mHABwHWuoXgfc1u3yRj+LcEmJlAbQ/YF04qF9CAep1QDqJ8xLP6rDcfAvA/BtCKq2eUy4EAewFbgJ2ppfXJ/nawYgCtet7l6MGPZyEGwLMYGPX48sPtxiYDrs5A/OFVR1esyIONUL74CuvOgDf4fx78QivfDnIC6fQfdXTlIUdEA6xu++1jXdCYvAJZu65LyZPuNOQg/9L/s+mDu9NKiSrYPRlFKc4cyN9zC5KdmRDPE1AzgzjmhBWxBF58TIxI6l6yvRlsWHu3/r4eai7HqhnxCSUdKo60wX575GgcWm6Bzh8P4FvAE1XXiMcXR06pHcGCK+4GmOAAktKW6+RE8H2dxCsWDTk6jqJ9NWbqIC1RuUi/HLTz4E/NJhQl1PzdbgStsHbmFLz39mvvT+bK4PHnQhj61aIZ04HfzcA4ryNfsN+cdLhDXTE8TjgyjHHH4maC8ZV+7UldzjF2JjpUVyVyc85FroQ0OPneDWPQFyKBjJAF5yyX1l4gIzz8+6UxJkeksz4QZ6E0hAEDUWDLvGN/hMMEM+GPvtdweleZ5gf0pO+8pDkmId6WAAHVB3OrY2ANfDer+uCQKpsEioBidMas3eETD519z1Ij9P5sAllYMiDYkFRMRsUYbuFyAjlpp0QkENAHVBmXDQGtlBKOHoat8xVaCOqgXSIDzcE8ZMAgbBFkUOtCRUj8H48SKQY9iEG+tgavQALkcGYZLjj+9aQVDSpXuzljvNdMcAcqozRMW3s/cSzs9pJMcZCBExL5QAR4H7WOrGpKmYIo/V64IqTnf1rprP+t9k1f9ax/GB7OZoIZGpN9nONBqKqA6miCn1Bhw3qu16HnA8JxJcMYUyYkWVNwUU6ds6VyrBOnOs/hk4v54Jr2Tts97u036oEM9HJsdCaVE0op4+w5WAD5BfmDFobLLdLzVKFeaU7xa922UQeb3f52LI8DsM0gDp2cENIVa+14EvaFLPZNqU4+Oh3anodvy/3YGcfYCCL/qvgK2xKjtOUEEjiEVsbyOPSV/0tOiHw31C6jSolVx9ykBh2uCf0r0mU1cjOGE8hZDmK44E24AqzReoiYQo10RLspm96ODZXajjjcjIs8nHQep2ZvZE+qqFjUuK1bBFbweg7S1/M/2+iZmmVhT4YAAAAASUVORK5CYII="/>
        </defs>
        </svg>      
    ),
    cosmos: (props: React.ComponentProps<"svg">) => (
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" {...props}>
        <rect width="32" height="32" rx="16" fill="url(#pattern0_14091_5770)"/>
        <defs>
        <pattern id="pattern0_14091_5770" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_14091_5770" transform="scale(0.03125)"/>
        </pattern>
        <image id="image0_14091_5770" width="32" height="32" preserveAspectRatio="none" xlinkHref="data:image/png;base64,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"/>
        </defs>
        </svg>

    ),
    nigeria: (props: React.ComponentProps<"svg">) => (
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" {...props}>
        <rect width="32" height="32" rx="16" fill="url(#pattern0_14091_19202)"/>
        <defs>
        <pattern id="pattern0_14091_19202" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_14091_19202" transform="scale(0.03125)"/>
        </pattern>
        <image id="image0_14091_19202" width="32" height="32" preserveAspectRatio="none" xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAIDSURBVHgB1ZdLTsMwEIZ/u7BDIkcoJwBOQMNjx6Mb1oQTtKzYUCiUDSvaExTWbEK3gAInKDcgN6BISH0IbGagRKEJj7apUT/JqmXHM7/Hj46BcWH3anm62WxmOp3OKZV6u91+pKK75ZHaPCpVqmf7sTvx2wdFL2uloHKAyEspLa113GfcnunWnZK34Qvg9gXisGi7/k/25U+dJS+bS0E/kPMiO8HfSZNMh8Z6RzdrDgYRcOStnwK63KfjiBAhZfXDVh8Cjr2NqqCQIyHYFtvEXwSwWg4fEoZtxkXiiwBeryRn3gvbJh/5WAG023m9DjBi2AefrIiAFLBFP2mMHksolY8IoFVyYAgpRW73anM6EFC6XuXbKw1zWFMTzflAgBIyA8MoJbOBAArJLAxDPhcCAcQczJMOCxjmuh0UKyzg3/gU0IB5GmEBPsxzHwhQSt/BMKI76e4xlC4MQ9nSZSDg+WWyDrP7wKdUzQ0EnKxcPCklKjAE54uf9eAYaglOv0xEwedkNSKAQtIQoY5RoXsy5S8X0Z7tlqH0CJdCV/Zt9yzcErkJC0u1PLQ+R/LOzwt2LZLuxV7FhcWak2wkdIWcO3E93/4XcCRovbYx3C3J+2onbua/CmB4vV4hbHxsTh99OOYxNHbmfV8lBT886YF6xg/R3scplXqr1SrzA5YfshgX3gA2vdwSs051zwAAAABJRU5ErkJggg=="/>
        </defs>
        </svg>
    ),
    ghana: (props: React.ComponentProps<"svg">) => (
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" {...props}>
        <rect width="32" height="32" rx="16" fill="url(#pattern0_14091_23783)"/>
        <defs>
        <pattern id="pattern0_14091_23783" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlinkHref="#image0_14091_23783" transform="scale(0.03125)"/>
        </pattern>
        <image id="image0_14091_23783" width="32" height="32" preserveAspectRatio="none" xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAMWSURBVHgBxZfPTxNBFMffzJK1mBLam2KMJeLJC6QgkJjIAVJCSOjVE/WC/EpqPWk8CAcSPEkjiQ0Jof4HcBBZMHEJRhCW0IPeNFkvSDxASUgA2+5zZuu20vJjtrbwSdqdnZ037/ve/NhZgAuGiDbUvN7KhNtdRyWpixm1IKIHCHGZDxHjhJAYK+msfqZxfn5atN8zBWy0tLiSshw0AB5lHJ6NzjpWU4QMN8/N6VCogFWfL8giGrLhOE+IYRjDzQsLUbAr4LPP9xJ41MVhrFFRQiAqgEU+hQABKCLMUfSOojzIrae5FTzyYjvn8D7/ZjVXWJbltrYApXQKSggTEmpSlLE8Acvt7R6K+IEVPVBa4vLhYXWdqsb5TWYICOLzc3DOcR3IcmZyZwWwzQXOCUpIUGttreTlMv6HWq0f6C8P2GRrm+knBK64U2ATF6SwDt6Dms6ARLugAJa+XoJ3mgMKgoKfX8rSd2xfL4CPTIBJG9iHkHtZAQi1IjbfNstgbz+dtL19ArHvslm2rhxnuQE1VUmR7jxZAXxMBHCWIzyLVsDWjnSkPhhJm/O5MBLYBUFMIwo24A7CfXG4e/sw71ntzQRMPt4WjT6DJSAuamBFybNhwcsj3XFwOhBscGQj0sEGS19kcw5wh1wQL/P5YZNYVgDiog1Dc/ZbKedDUlOVYMuxHGxBUM8KMED4CMW5xcY53LtjRs9/k6EdVpcAW6RgxtTB/3pGvZVSBdXZXaEnH5sQ/XX/ajUvmRmYeLK+i4BhOCcIQdUqZ5ah43dyjG1IwqvhP9zrBkjD1l1mR1lRtg4aOqvYAiftUEIQIRTpX1XzBHC0t5sr9R1X3eyM3wQlgDkPRwbWRv+tO/ZQ2jveECUUuqGYzg14ExlcC+TWS8c11mY3p4uZCTPywbXe455JJxlpsz/n6juu/WAi+JuysOXJJzXBp5EBbeikJtJp9iwTsfrO62zDQDcB8+tITAhzzJb1C8dl5/3xnk/qaU2FP045fa+8fpSon63jG2CwzFgbVzpSHQlZhCROGzJuTDxcF34vXyh/AAMhDgugLQo0AAAAAElFTkSuQmCC"/>
        </defs>
        </svg>

    ),
    edit: (props: React.ComponentProps<"svg">) => (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path d="M13.2633 3.60022L5.05327 12.2902C4.74327 12.6202 4.44327 13.2702 4.38327 13.7202L4.01327 16.9602C3.88327 18.1302 4.72327 18.9302 5.88327 18.7302L9.10327 18.1802C9.55327 18.1002 10.1833 17.7702 10.4933 17.4302L18.7033 8.74022C20.1233 7.24022 20.7633 5.53022 18.5533 3.44022C16.3533 1.37022 14.6833 2.10022 13.2633 3.60022Z" stroke="#25292D" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M11.8906 5.0498C12.3206 7.8098 14.5606 9.9198 17.3406 10.1998" stroke="#25292D" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M3 22H21" stroke="#25292D" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    ),
    delete: (props: React.ComponentProps<"svg">) => (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path d="M21 5.97998C17.67 5.64998 14.32 5.47998 10.98 5.47998C9 5.47998 7.02 5.57998 5.04 5.77998L3 5.97998" stroke="#292D32" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M8.5 4.97L8.72 3.66C8.88 2.71 9 2 10.69 2H13.31C15 2 15.13 2.75 15.28 3.67L15.5 4.97" stroke="#292D32" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M18.8484 9.14014L18.1984 19.2101C18.0884 20.7801 17.9984 22.0001 15.2084 22.0001H8.78844C5.99844 22.0001 5.90844 20.7801 5.79844 19.2101L5.14844 9.14014" stroke="#292D32" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M10.3281 16.5H13.6581" stroke="#292D32" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M9.5 12.5H14.5" stroke="#292D32" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    ),
}