"use client"
import React, { useState } from 'react'
import { ControllerRenderProps, FieldPath, FieldValues, useWatch } from 'react-hook-form'
import { Input } from '../ui/input';
import { cn } from '@/common/lib/utils';

interface PlaceholderProps<T extends FieldValues, TName extends FieldPath<T>> {
    field?: ControllerRenderProps<T, TName>;
    name?: string;
    placeholder?: string
    hidePlaceholderOnFocus?: boolean
    hideBorder?: boolean
    hidePlaceholderOnType?: boolean
    onValueChange?: (value: string|number) => void,
    classes?: string;
    value?: string;
}

export default function PlaceholderFloatInput<T extends FieldValues, TName extends FieldPath<T>>({
    field,
    placeholder,
    hidePlaceholderOnFocus,
    hideBorder,
    hidePlaceholderOnType,
    onValueChange,
    classes,
    value = '',
    ...props
}: PlaceholderProps<T, TName> & React.InputHTMLAttributes<HTMLInputElement>) {

  const watchedValue = useWatch({
    name: field?.name as TName,
    disabled: !field
  });

  const fieldValue = field ? watchedValue : value;

  const [isFocused, setIsFocused] = useState(false);
  const [hasValue, setHasValue] = useState(false);

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (field) {
      field.onChange(e);
    }
    setHasValue(e.target.value !== '');

    if(onValueChange) {
      onValueChange(e.target.value)
    }
  };

  return (
    <div>
      <div className="relative">
          <Input
              type="text"
              {...field}
              id={field?.name}
              className={cn(
                  `text-sm md:text-[14px] text-dark important block w-full h-[50px] md:h-[60px] rounded-full`,
                  (placeholder && (fieldValue || isFocused)) ? hidePlaceholderOnFocus? 'px-5': 'pt-6 px-5' : 'p-5',
                  (hideBorder) ? 'border-none' : '',
                  classes
              )}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onChange={handleInputChange}
              {...props}
          />
          {placeholder && (
              <label
                  className={cn(
                    `absolute left-0 p-5 transition-all duration-200 select-none pointer-events-none`,
                    (fieldValue || isFocused) ? (!hidePlaceholderOnType && hidePlaceholderOnFocus)? 'opacity-0' : 'top-[-15px] text-[12px] text-dark' : 'text-placeholder -top-1 md:top-0',
                    hidePlaceholderOnType && hasValue? 'opacity-0' : ''
                  )}
                  htmlFor={field?.name}
                >
                  {placeholder}
              </label>
          )}
      </div>
    </div>
  )
}
