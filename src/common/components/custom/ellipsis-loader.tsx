
interface LoopLoaderProps {
    delay?: number[];
    size?: number;
}

export default function EllipsisLoader({ delay = [0, 0.15, 0.3], size = 10 }: LoopLoaderProps) {
    return (
        <div className="flex gap-1">
        {delay.map((d) => (
            <div
            key={d}
            className="h-3 w-3 rounded-full bg-orange animate-[bounce_1.4s_ease-in-out_infinite]"
            style={{ animationDelay: `${d}s`, width: `${size}px`, height: `${size}px` }}
            />
        ))}
        </div>
    );
}
  