import Image from 'next/image'
import React from 'react'
import '@/common/styles/auth-form.css'
import { Icons } from './icon-templates'

export default function AuthPageLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className='relative grid grid-cols-1 lg:grid-cols-3'>

      <div className="absolute inset-0 bg-white opacity-20 bg-bg -z-10" />

      <main className='col-span-2 w-full min-h-screen md:py-10 pb-10 px-5 sm:px-7 md:px-10 xl:px-32 space-y-10 flex items-center justify-center'>
        <div className='border bg-white p-4 md:py-6 md:px-10 rounded-2xl'>{children}</div>
      </main>

      <aside className='bg-orange-fainter h-screen hidden lg:flex flex-col justify-between sticky top-0 px-5 py-10'>

        <header className='hidden md:block'>
          <Icons.logo />
        </header>

        <div className='flex flex-col items-center text-center space-y-4'>
          <div className='text-4xl font-bold text-orange'>Your Payment Passport</div>
          <p className='text-2xl text-black'>Experience seamless payments</p>
        </div>

        <div>
          <Image
            alt="payment-ads-image"
            width={100}
            height={100}
            className='w-[85%] h-auto'
            src="/images/brand-img.svg"
          />
        </div>
      </aside>

    </div>

  )
}
