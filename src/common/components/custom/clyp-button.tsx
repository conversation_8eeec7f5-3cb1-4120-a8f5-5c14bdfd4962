import React from 'react'
import { But<PERSON> } from '../ui/button'
import { cn } from '@/common/lib/utils'
import { LoaderIcon } from 'lucide-react'

interface ClypButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  text?: string,
  isLoading?: boolean,
  children?: React.ReactNode,
  classes?: string,
  variant?: 'default' | 'outline'
}

const outlineClassList = `bg-inherit text-orange text-body-2 ring-1 ring-orange`;
const defaultClassList = `text-white bg-orange ring-0 hover:bg-orange-600`;

export default function ClypButton({
  classes, 
  text, 
  type, 
  isLoading, 
  children, 
  variant, 
  ...props
}: ClypButtonProps) {
  
  const variantClasses = variant === 'outline' ? outlineClassList : defaultClassList
  
  return (
    <Button 
        {...props}
        className={cn(
          '!rounded-full shadow-none hover:shadow text-medium-18',
          'flex items-center justify-center gap-2',
          variantClasses, 
          classes || props.className
        )}
        type={type || 'button'}
    >
      {isLoading ? <LoaderIcon className="animate-spin" /> : children ?? text}
    </Button>
  )
}
