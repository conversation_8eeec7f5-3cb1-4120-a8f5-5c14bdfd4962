import { useQuery } from "@tanstack/react-query";
import { queryKeys } from "./query-keys";
import { GetNetworkList, GetNGNBanks, ResolveAccount } from "../api";
import { TResolveAccount } from "../interfaces";

export const useGetNGNBanks = (enabled?: boolean) => {
    console.log('getting NGN banks', enabled)
    return useQuery({
        queryKey: [queryKeys.getNGNBanks], 
        queryFn: GetNGNBanks, 
        enabled
    })
}

export const useResolveAccount = (data: TResolveAccount, enabled?: boolean) => {
    console.log('resolving account', data)
    return useQuery({
        queryKey: [queryKeys.resolveAccount(data)],
        queryFn: () => ResolveAccount(data),
        enabled
    })
}
    
export const useGetNetworkList = (currency: string, enabled?: boolean) => {
    return useQuery({
        queryKey: [queryKeys.networkList(currency)],
        queryFn: () => GetNetworkList(currency),
        enabled: enabled? enabled : !!currency
    })
}