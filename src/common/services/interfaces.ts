export interface TResponse<T> {
    data: T,
    message: string,
    statusCode: number
}

export interface TNGNBanks {
    id: number;
    name: string;
    slug: string;
    code: string;
    longcode: string;
    gateway: string | null;
    pay_with_bank: boolean;
    supports_transfer: boolean;
    active: boolean;
    country: string;
    currency: string;
    type: string;
    is_deleted: boolean;
    createdAt: string;
    updatedAt: string;
}

export interface TResolveAccount {
    account_number: string;
    bank_code: string;
}

export interface TResolveAccountData {
    account_name: string;
    account_number: string;
    bank_id: string;
}

export interface TNGNBanksResponse {
    data: TNGNBanks[];
    message?: string;
    status: number;
}

export interface TResolveAccountResponse {
    data: TResolveAccountData;
    message?: string;
    status: number;
}

export interface SearchParams {
    [key: string]: string | string[] | undefined;
}


export type IndexPageProps = {
    searchParams: Promise<{
        [key: string]: string | string[] | undefined;
    }>;
};

export type IndexPageParamProps = {
    params: Promise<{
        [key: string]: string | string[] | undefined;
    }>;
};

export type IndexClientProps = {
    searchParams: SearchParams;
};

export interface BasicTableSchema {
    uid: string;
    deleted_at?: string;
    created_at: string;
    updated_at: string;
}

export enum CheckoutPaymentChannel {
    BANK_TRANSFER = 'BANK_TRANSFER',
    CRYPTO = 'CRYPTO',
}
  