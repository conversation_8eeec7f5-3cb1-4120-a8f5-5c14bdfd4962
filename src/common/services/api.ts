import { TNGNBanksResponse, TResolveAccountResponse, TResolveAccount, TResponse } from "@/common/services/interfaces";
import { getRoutes } from "@/common/services/constants";
import { apiRequest } from "../lib/api-request";


export const GetNGNBanks = async () => 
    await apiRequest<undefined, TNGNBanksResponse>({
        path: getRoutes.getNGNBanks().path,
        method: getRoutes.getNGNBanks().method,
        headers: {
            'Authorization': `Bearer ${process.env.NEXT_PUBLIC_PAYSTACK_SECRET_KEY}`
        },
        externalLink: true
      });

export const ResolveAccount = async ({account_number, bank_code}: TResolveAccount) => 
    await apiRequest<TResolveAccount, TResolveAccountResponse>({
        path: getRoutes.resolveAccount(account_number, bank_code).path,
        method: getRoutes.resolveAccount(account_number, bank_code).method,
        headers: {
            'Authorization': `Bear<PERSON> ${process.env.NEXT_PUBLIC_PAYSTACK_SECRET_KEY}`
        },
        externalLink: true
      });

export const GetNetworkList = async (currency: string) => {
    return await apiRequest<undefined, TResponse<string[]>>({
        path: getRoutes.getNetworkList(currency).path,
        method: getRoutes.getNetworkList(currency).method,
    })
}