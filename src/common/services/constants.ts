import { WalletTypeEnum } from "@/app/(dashboard)/balance/[walletId]/history/_services/interfaces";
import { ApiMethod } from "@/common/lib/api-request";

export const queryStaleTime =  60 * 1000;

export const ACCESS_TOKEN_KEY = '_token'

export const ORGANIZATION_UID_KEY = "organization_uid";

export const LOGIN_MAX_AGE = 60 * 60 * 5;

export const REFRESH_TOKEN_KEY = '_refresh_token'

export const REFRESH_TOKEN_MAX_AGE = 60 * 60 * 24 * 7;


const basePath = `api/v1`;

interface IApiRoutes {
    path: string;
    method: ApiMethod;
}

// Disable the eslint rule for this line
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const getRoutes: Record<string, (...args: any[]) => IApiRoutes> = {
    // Bank routes
    getNGNBanks: () => ({
        path: `https://api.paystack.co/bank`,
        method: 'GET'
    }),
    resolveAccount: (account_number: string, bank_code: string) => ({
        path: `https://api.paystack.co/bank/resolve?account_number=${account_number}&bank_code=${bank_code}`,
        method: 'GET'
    }),
    getNetworkList: (currency: string) => ({
        path: `${basePath}/internal/currency/crypto/networks?currency=${currency}`,
        method: "GET",
    }),


    // Wallet routes
    getWallets: (owner_uid: string) => ({
        path: `${basePath}/internal/wallet?owner_uid=${owner_uid}`,
        method: 'GET'
    }),
    createWallet: (owner_uid: string) => ({
        path: `${basePath}/internal/wallet?owner_uid=${owner_uid}`,
        method: 'POST'
    }),
    getCurrencies: (wallet_type: WalletTypeEnum) => ({
        path: `${basePath}/internal/wallet/currencies?type=${wallet_type}`,
        method: 'GET'
    }),
    // deposit here...
    deposit: () => ({
        path: `${basePath}/internal/transaction/organization/initialize`,
        method: 'POST'
    }),

    getTransferFee: (wallet_uid: string) => ({
        path: `${basePath}/internal/wallet/${wallet_uid}/transactions/fee`,
        method: 'GET'
    }),
    fiatTransferResolve: () => ({
        path: `${basePath}/internal/wallet/fiat/transfer/resolve-account`,
        method: 'POST'
    }),
    cryptoTransferResolve: () => ({
        path: `${basePath}/internal/wallet/crypto/transfer/resolve-account`,
        method: 'POST'
    }),
    initiateWithdraw: (wallet_uid: string) => ({
        path: `${basePath}/internal/wallet/${wallet_uid}/fiat/withdraw/initiate`,
        method: 'POST'
    }),
    getTransactionHistory: (wallet_uid: string) => ({
        path: `${basePath}/internal/wallet/${wallet_uid}/transactions/history`,
        method: 'GET'
    }),


    // Currency conversion routes
    lockConversionRate: () => ({
        path: `${basePath}/internal/currency/conversions/rate/lock`,
        method: 'POST'
    }),
    convertCurrency: () => ({
        path: `${basePath}/internal/currency/conversions/convert`,
        method: 'POST'
    }),

    getConversionRate: () => ({
        path: `${basePath}/internal/currency/conversions/rate`,
        method: 'GET'
    }),
    getConversionHistory: () => ({
        path: `${basePath}/internal/currency/conversions/history`,
        method: 'GET'
    }),


    // Auth routes
    register: () => ({
        path: `${basePath}/internal/auth/register`,
        method: 'POST'
    }),
    login: () => ({
        path: `${basePath}/internal/auth/login`,
        method: 'POST'
    }),
    logout: () => ({
        path: `${basePath}/internal/auth/logout`,
        method: 'POST'
    }),
    refreshToken: () => ({
        path: `${basePath}/internal/auth/refresh`,
        method: 'POST'
    }),
    getProfile: () => ({
        path: `${basePath}/internal/auth/profile`,
        method: 'GET'
    }),
    forgotPassword: () => ({
        path: `${basePath}/internal/auth/forgot-password`,
        method: 'POST'
    }),
    resetPassword: () => ({
        path: `${basePath}/internal/auth/reset-password`,
        method: 'POST'
    }),
    generateApiKey: () => ({
        path: `${basePath}/internal/auth/api-key`,
        method: 'POST'
    }),

    // Organization routes
    createOrganization: () => ({
        path: `${basePath}/internal/organization`,
        method: 'POST'
    }),
    getOrganization: () => ({
        path: `${basePath}/internal/organization`,
        method: 'GET'
    }),
    submitCompliance: () => ({
        path: `${basePath}/internal/organization/compliance/submit`,
        method: 'POST'
    }),
    uploadComplianceDoc: () => ({
        path: `${basePath}/internal/organization/compliance/upload`,
        method: 'POST'
    }),
    submitComplianceDocs: () => ({
        path: `${basePath}/internal/organization/compliance/submit`,
        method: 'POST'
    }),
    removeUploadedDoc: (file_id: string) => ({
        path: `${basePath}/internal/organization/compliance/docs/${file_id}`,
        method: 'DELETE'
    }),

    // Customer routes
    createCustomer: () => ({
        path: `${basePath}/internal/customer`,
        method: 'POST'
    }),
    getCustomer: (customer_uid: string) => ({
        path: `${basePath}/internal/customer/${customer_uid}`,
        method: 'GET'
    }),
    updateCustomer: (customer_uid: string) => ({
        path: `${basePath}/internal/customer/${customer_uid}`,
        method: 'PATCH'
    }),
    deleteCustomer: (customer_uid: string) => ({
        path: `${basePath}/internal/customer/${customer_uid}`,
        method: 'DELETE'
    }),
    getCustomers: () => ({
        path: `${basePath}/internal/customer`,
        method: 'GET'
    }),

    // Transfer routes
    getTransferRecipients: () => ({
        path: `${basePath}/transfer_recipient`,
        method: 'GET'
    }),
    createTransferRecipient: () => ({
        path: `${basePath}/transfer_recipient`,
        method: 'POST'
    }),
    getTransferRecipient: (recipient_uid: string) => ({
        path: `${basePath}/transfer_recipient/${recipient_uid}`,
        method: 'GET'
    }),
    // eslint-disable-next-line
    createWidthdrawal: (wallet_uid: string) => ({
        path: `${basePath}/internal/transfer`,
        method: 'POST'
    }),

}
