@tailwind base;
@tailwind components;
@tailwind utilities;

/*
NOTE: ALL COLOURS ARE CONVERTED TO HSL
CUSTOM FONTS: Ensure to handle the Dark mode version of the color 
*/
@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground text-[#25292D];
  }

  :root {
    --sidebar-background: 34 84% 95% 1;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;

    /*COLOUR: GREEN*/
    --green-900: 165 79% 22%;
    --green-600: 96, 65%, 40%, 1;
    --green-700: 142 79% 31%;
    --green-400: 121 35% 52%;
    --green-100: 101 96% 82%;
    --green-50: 101 96% 94%;

    /*COLOUR: ORANGE*/
    --orange-900: 27 98% 39%;
    --orange-600: 30 100% 55%;
    --orange-200: 35 100% 80%;
    --orange-50: 36 100% 95%;

    /*COLOUR: NEUTRAL*/
    --neutral-900: 0 0% 10%;
    --neutral-800: 0 0% 29%;
    --neutral-600: 0 0% 50%;
    --neutral-400: 0 0% 68%;
    --neutral-200: 0 0% 90%;
    --neutral-100: 0 0% 97%;
    --neutral-default: 0 0% 100%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --sidebar-background: 94 62% 95% 1;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /*COLOUR: GREEN*/
    --green-900: 165 58% 45%;
    /* Lighter for dark mode */
    --green-700: 142 63% 50%;
    --green-400: 121 40% 62%;
    --green-100: 101 44% 72%;
    --green-50: 101 50% 80%;

    /*COLOUR: BLUE*/
    --blue-800: 226 55% 45%;
    /* Slightly lighter */
    --blue-600: 213 68% 60%;
    --blue-100: 210 75% 75%;
    --blue-50: 208 83% 85%;

    /*COLOUR: RED*/
    --red-900: 351 75% 50%;
    /* More vibrant */
    --red-600: 355 80% 60%;
    --red-100: 351 85% 75%;
    --red-50: 348 90% 85%;

    /*COLOUR: ORANGE*/
    --orange-900: 27 88% 50%;
    /* Brighter */
    --orange-600: 30 90% 60%;
    --orange-200: 35 93% 72%;
    --orange-50: 36 96% 85%;

    /*COLOUR: YELLOW*/
    --yellow-900: 47 90% 40%;
    /* Slightly muted */
    --yellow-600: 50 95% 55%;
    --yellow-200: 47 96% 70%;
    --yellow-50: 47 100% 85%;

    /*COLOUR: PURPLE*/
    --purple-900: 252 55% 55%;
    /* Enhanced contrast */
    --purple-600: 277 65% 65%;
    --purple-100: 291 75% 80%;
    --purple-50: 293 85% 90%;

    /*COLOUR: NEUTRAL*/
    --neutral-900: 0 0% 85%;
    /* Inverse from dark to light */
    --neutral-800: 0 0% 70%;
    --neutral-600: 0 0% 60%;
    --neutral-400: 0 0% 50%;
    --neutral-200: 0 0% 35%;
    --neutral-100: 0 0% 25%;
    --neutral-default: 0 0% 15%;
    /* Close to black */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}


@layer components {
  input:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 60px white inset !important;
    box-shadow: 0 0 0 60px white inset !important;
  }

  input:focus:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 60px white inset !important;
    box-shadow: 0 0 0 60px white inset !important;
  }
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  /* Standard width for vertical scrollbar */
  height: 8px;
  /* Standard height for horizontal scrollbar */
} 

/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
  /* Light gray background */
  border-radius: 4px;
  /* Slightly rounded corners */
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #888;
  /* Neutral gray color */
  border-radius: 4px;
  /* Slightly rounded corners */
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
  /* Darker gray on hover */
}


/* Fonts */
.nunito {
  font-family: 'Nunito Sans', sans-serif;
}

.montserrat {
  font-family: 'Montserrat', sans-serif;
}

.clear-disabled-input input {
  @apply !opacity-100;
}

.bg-bg {
  background: url("/images/auth-bacakground.jpg");
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}