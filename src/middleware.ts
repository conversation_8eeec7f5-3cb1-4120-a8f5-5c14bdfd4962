import {
    SIGNIN_ROUTE,
    DASHBOARD_ROUTE,
    PUBLIC_ROUTES,
    CREATE_ORGANIZATION,
  } from "@/common/lib/routes";
  import { cookies } from "next/headers";
  import { NextRequest, NextResponse } from "next/server";
import { ACCESS_TOKEN_KEY, ORGANIZATION_UID_KEY } from "./common/services/constants";
import { GetProfile } from "./app/(auth)/_services/api";
  
  export async function middleware(req: NextRequest) {
    const { nextUrl } = req;
    const token = (await cookies()).get(ACCESS_TOKEN_KEY)?.value;
  
    const isPublicRoute =
      PUBLIC_ROUTES.some((route) => nextUrl.pathname.match(new RegExp(route)))  
      
    // Skip API calls and redirects for public routes when no token exists
    if (!token && isPublicRoute) {
     
      return NextResponse.next();
     
    }

    try {
      const callbackUrl = nextUrl.searchParams.get("callbackUrl");
      const newRoute = callbackUrl ? new URL(callbackUrl, nextUrl) : new URL(DASHBOARD_ROUTE, nextUrl)
      
      const response = await GetProfile()
      const {data} = response

      // Token unauthorized → sign in
      if(response.statusCode === 401) {
        // Consider clearing the token cookie here to prevent loops
        return NextResponse.redirect(new URL(SIGNIN_ROUTE, nextUrl))
      }
      
      // No account and not public route → sign in
      if(!data.account && !isPublicRoute) {
        return NextResponse.redirect(new URL(SIGNIN_ROUTE, nextUrl))
      }
  
      // Has account and on public route → dashboard or create-org
      if(data?.account && isPublicRoute) {
        if(data.organization) {
          const response = NextResponse.redirect(new URL(DASHBOARD_ROUTE, newRoute));
          // Set organization UID cookie if organization exists
          if (data.organization?.uid) {
            response.cookies.set(ORGANIZATION_UID_KEY, data.organization.uid);
          }
          return response;
        } else {
          return NextResponse.redirect(new URL(CREATE_ORGANIZATION, newRoute))
        }
      }        
  
      // Has account but no organization → create-org (except if already on create-org)
      if(!data.organization && nextUrl.pathname !== CREATE_ORGANIZATION) {
        return NextResponse.redirect(new URL(CREATE_ORGANIZATION, newRoute))
      } else if(data.organization && nextUrl.pathname === CREATE_ORGANIZATION) {
        const response = NextResponse.redirect(new URL(DASHBOARD_ROUTE, newRoute));
        // Set organization UID cookie if organization exists
        if (data.organization?.uid) {
          response.cookies.set(ORGANIZATION_UID_KEY, data.organization.uid);
        }
        return response;
      } else if(data.organization && nextUrl.pathname !== CREATE_ORGANIZATION) {
        const response = NextResponse.next();
        // Set organization UID cookie if organization exists
        if (data.organization?.uid) {
          response.cookies.set(ORGANIZATION_UID_KEY, data.organization.uid);
        }
        return response;
      }

      return NextResponse.next()
    } catch {
      // For errors, better to just allow navigation rather than risk a redirect loop
      if (isPublicRoute) {
        return NextResponse.next();
      }
      return NextResponse.redirect(new URL(SIGNIN_ROUTE, nextUrl))
    }
  

    

  }
  
  export const config = {
    matcher: ["/((?!.+\\.[\\w]+$|_next).*)", "/", "/(api|trpc)(.*)"],
  };
  