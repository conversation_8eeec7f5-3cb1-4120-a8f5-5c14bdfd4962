{"name": "payment-business-webapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "compile": "tsc", "lint:unused": "eslint --fix-dry-run \"src/**/*.{js,jsx,ts,tsx}\" --rule \"unused-imports/no-unused-imports: error\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\" --rule \"unused-imports/no-unused-imports: error\"", "heroku-postbuild": "yarn install --no-frozen-lockfile && yarn build"}, "dependencies": {"@bprogress/next": "^3.2.12", "@hookform/resolvers": "^4.1.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "4", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-table": "^8.21.2", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookies-next": "^5.1.0", "date-fns": "^4.1.0", "input-otp": "^1.4.2", "lucide-react": "^0.475.0", "next": "15.4.2-canary.7", "next-themes": "^0.4.4", "react": "^19.0.0", "react-day-picker": "9.8.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-is": "^19.1.0", "recharts": "^3.1.0", "sonner": "^2.0.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "typescript": "5.8.3", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tanstack/eslint-plugin-query": "4", "eslint": "^9", "eslint-config-next": "15.1.7", "eslint-plugin-unused-imports": "^4.1.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}